#include "metrics_collector.h"
#include "logging.h"
#include <mutex>
#include <unordered_map>
#include <vector>
#include <thread>
#include <condition_variable>
#include <random>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <regex>

namespace omop::monitoring {

// Implementation class for MetricsCollector
class MetricsCollector::Impl {
public:
    Impl() : next_timer_id_(0), collection_thread_running_(false) {}
    
    ~Impl() {
        stop_collection_thread();
    }

    bool initialize(const MetricsConfig& config) {
        std::lock_guard<std::mutex> lock(mutex_);
        config_ = config;
        
        if (config_.enabled) {
            start_collection_thread();
        }
        
        auto logger = omop::common::Logger::get("omop-metrics");
        logger->info("MetricsCollector initialized with config: enabled={}, interval={}s, retention={}s",
                    config_.enabled, config_.collection_interval.count(), config_.retention_period.count());
        
        return true;
    }

    bool register_metric(const MetricDefinition& definition) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (metrics_.find(definition.name) != metrics_.end()) {
            return false; // Already exists
        }
        
        Metric metric;
        metric.definition = definition;
        metric.last_updated = std::chrono::system_clock::now();
        
        // Initialize histogram data if needed
        if (definition.type == MetricType::Histogram) {
            HistogramData hist_data;
            hist_data.total_count = 0;
            hist_data.sum = 0.0;
            
            for (double bound : definition.histogram_buckets) {
                hist_data.buckets.push_back({bound, 0});
            }
            // Add +Inf bucket
            hist_data.buckets.push_back({std::numeric_limits<double>::infinity(), 0});
            
            metric.histogram_data = hist_data;
        }
        
        // Initialize summary data if needed
        if (definition.type == MetricType::Summary) {
            SummaryData summary_data;
            summary_data.count = 0;
            summary_data.sum = 0.0;
            
            for (double quantile : definition.summary_quantiles) {
                summary_data.quantiles.push_back({quantile, 0.0});
            }
            
            metric.summary_data = summary_data;
        }
        
        metrics_[definition.name] = metric;
        
        auto logger = omop::common::Logger::get("omop-metrics");
        logger->debug("Registered metric: {} (type: {})", definition.name, 
                     metric_type_to_string(definition.type));
        
        return true;
    }

    bool increment_counter(const std::string& name, double value, 
                          const std::unordered_map<std::string, std::string>& labels) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = metrics_.find(name);
        if (it == metrics_.end() || it->second.definition.type != MetricType::Counter) {
            return false;
        }
        
        MetricValue metric_value;
        metric_value.value = value;
        metric_value.timestamp = std::chrono::system_clock::now();
        metric_value.labels = labels;
        
        it->second.values.push_back(metric_value);
        it->second.last_updated = metric_value.timestamp;
        
        cleanup_old_values(it->second);
        
        return true;
    }

    bool set_gauge(const std::string& name, double value, 
                   const std::unordered_map<std::string, std::string>& labels) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = metrics_.find(name);
        if (it == metrics_.end() || it->second.definition.type != MetricType::Gauge) {
            return false;
        }
        
        MetricValue metric_value;
        metric_value.value = value;
        metric_value.timestamp = std::chrono::system_clock::now();
        metric_value.labels = labels;
        
        // For gauges, we typically keep only the latest value per label set
        auto& values = it->second.values;
        auto existing = std::find_if(values.begin(), values.end(), 
            [&labels](const MetricValue& mv) { return mv.labels == labels; });
        
        if (existing != values.end()) {
            *existing = metric_value;
        } else {
            values.push_back(metric_value);
        }
        
        it->second.last_updated = metric_value.timestamp;
        
        cleanup_old_values(it->second);
        
        return true;
    }

    bool observe_histogram(const std::string& name, double value, 
                          const std::unordered_map<std::string, std::string>& labels) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = metrics_.find(name);
        if (it == metrics_.end() || it->second.definition.type != MetricType::Histogram) {
            return false;
        }
        
        if (!it->second.histogram_data) {
            return false;
        }
        
        auto& hist_data = it->second.histogram_data.value();
        hist_data.total_count++;
        hist_data.sum += value;
        
        // Update buckets
        for (auto& bucket : hist_data.buckets) {
            if (value <= bucket.upper_bound) {
                bucket.count++;
            }
        }
        
        it->second.last_updated = std::chrono::system_clock::now();
        
        return true;
    }

    bool observe_summary(const std::string& name, double value, 
                        const std::unordered_map<std::string, std::string>& labels) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = metrics_.find(name);
        if (it == metrics_.end() || it->second.definition.type != MetricType::Summary) {
            return false;
        }
        
        if (!it->second.summary_data) {
            return false;
        }
        
        auto& summary_data = it->second.summary_data.value();
        summary_data.count++;
        summary_data.sum += value;
        
        // Store value for quantile calculation
        summary_values_[name].push_back(value);
        
        // Keep only recent values for quantile calculation
        auto& values = summary_values_[name];
        if (values.size() > 1000) {
            values.erase(values.begin(), values.begin() + 500);
        }
        
        // Update quantiles
        calculate_quantiles(name, summary_data);
        
        it->second.last_updated = std::chrono::system_clock::now();
        
        return true;
    }

    std::string start_timer(const std::string& name, 
                           const std::unordered_map<std::string, std::string>& labels) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        std::string timer_id = std::to_string(next_timer_id_++);
        
        TimerInfo timer_info;
        timer_info.metric_name = name;
        timer_info.labels = labels;
        timer_info.start_time = std::chrono::high_resolution_clock::now();
        
        active_timers_[timer_id] = timer_info;
        
        return timer_id;
    }

    std::optional<double> stop_timer(const std::string& timer_id) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = active_timers_.find(timer_id);
        if (it == active_timers_.end()) {
            return std::nullopt;
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::duration<double>>(
            end_time - it->second.start_time);
        
        double elapsed_seconds = duration.count();
        
        // Record the timer value as a histogram observation directly (avoid deadlock)
        auto metric_it = metrics_.find(it->second.metric_name);
        if (metric_it != metrics_.end() && metric_it->second.definition.type == MetricType::Histogram) {
            if (metric_it->second.histogram_data) {
                auto& hist_data = metric_it->second.histogram_data.value();
                hist_data.total_count++;
                hist_data.sum += elapsed_seconds;
                
                // Update buckets
                for (auto& bucket : hist_data.buckets) {
                    if (elapsed_seconds <= bucket.upper_bound) {
                        bucket.count++;
                    }
                }
                
                metric_it->second.last_updated = std::chrono::system_clock::now();
            }
        }
        
        active_timers_.erase(it);
        
        return elapsed_seconds;
    }

    std::optional<Metric> get_metric(const std::string& name) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = metrics_.find(name);
        if (it == metrics_.end()) {
            return std::nullopt;
        }
        
        return it->second;
    }

    std::vector<Metric> query_metrics(const MetricsQuery& query) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        std::vector<Metric> results;
        
        for (const auto& [name, metric] : metrics_) {
            if (query.name_pattern && !std::regex_match(name, std::regex(*query.name_pattern))) {
                continue;
            }
            
            if (query.type && metric.definition.type != *query.type) {
                continue;
            }
            
            if (query.start_time && metric.last_updated < *query.start_time) {
                continue;
            }
            
            if (query.end_time && metric.last_updated > *query.end_time) {
                continue;
            }
            
            results.push_back(metric);
        }
        
        // Apply offset and limit
        if (query.offset < results.size()) {
            results.erase(results.begin(), results.begin() + query.offset);
        } else {
            results.clear();
        }
        
        if (query.limit < results.size()) {
            results.resize(query.limit);
        }
        
        return results;
    }

    std::vector<std::string> get_metric_names() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        std::vector<std::string> names;
        names.reserve(metrics_.size());
        
        for (const auto& [name, _] : metrics_) {
            names.push_back(name);
        }
        
        return names;
    }

    std::string export_metrics(const std::string& format) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (format == "prometheus") {
            return export_prometheus_format();
        } else if (format == "json") {
            return export_json_format();
        }
        
        return "";
    }

    bool clear_metrics(const std::string& name) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (name.empty()) {
            metrics_.clear();
            summary_values_.clear();
            return true;
        }
        
        auto it = metrics_.find(name);
        if (it != metrics_.end()) {
            metrics_.erase(it);
            summary_values_.erase(name);
            return true;
        }
        
        return false;
    }

    size_t delete_old_data(const std::chrono::system_clock::time_point& older_than) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        size_t deleted_count = 0;
        
        for (auto& [name, metric] : metrics_) {
            auto& values = metric.values;
            auto original_size = values.size();
            
            values.erase(std::remove_if(values.begin(), values.end(),
                [&older_than](const MetricValue& mv) {
                    return mv.timestamp < older_than;
                }), values.end());
            
            deleted_count += (original_size - values.size());
        }
        
        return deleted_count;
    }

    std::unordered_map<std::string, std::any> get_statistics() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        std::unordered_map<std::string, std::any> stats;
        
        stats["total_metrics"] = metrics_.size();
        stats["active_timers"] = active_timers_.size();
        stats["config_enabled"] = config_.enabled;
        stats["collection_interval_seconds"] = config_.collection_interval.count();
        stats["retention_period_seconds"] = config_.retention_period.count();
        
        size_t total_values = 0;
        for (const auto& [name, metric] : metrics_) {
            total_values += metric.values.size();
        }
        stats["total_metric_values"] = total_values;
        
        return stats;
    }

    MetricsConfig get_config() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return config_;
    }

    bool update_config(const MetricsConfig& config) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        bool restart_thread = (config_.enabled != config.enabled || 
                              config_.collection_interval != config.collection_interval);
        
        config_ = config;
        
        if (restart_thread) {
            stop_collection_thread();
            if (config_.enabled) {
                start_collection_thread();
            }
        }
        
        return true;
    }

private:
    struct TimerInfo {
        std::string metric_name;
        std::unordered_map<std::string, std::string> labels;
        std::chrono::high_resolution_clock::time_point start_time;
    };

    mutable std::mutex mutex_;
    MetricsConfig config_;
    std::unordered_map<std::string, Metric> metrics_;
    std::unordered_map<std::string, TimerInfo> active_timers_;
    std::unordered_map<std::string, std::vector<double>> summary_values_;
    std::atomic<uint64_t> next_timer_id_;
    
    std::thread collection_thread_;
    std::atomic<bool> collection_thread_running_;
    std::condition_variable collection_cv_;

    void cleanup_old_values(Metric& metric) {
        auto& values = metric.values;
        if (values.size() > config_.max_values_per_metric) {
            values.erase(values.begin(), values.begin() + (values.size() - config_.max_values_per_metric));
        }
    }

    void calculate_quantiles(const std::string& name, SummaryData& summary_data) {
        auto it = summary_values_.find(name);
        if (it == summary_values_.end() || it->second.empty()) {
            return;
        }
        
        auto values = it->second;
        std::sort(values.begin(), values.end());
        
        for (auto& quantile : summary_data.quantiles) {
            double index = quantile.quantile * (values.size() - 1);
            size_t lower_index = static_cast<size_t>(std::floor(index));
            size_t upper_index = static_cast<size_t>(std::ceil(index));
            
            if (lower_index == upper_index) {
                quantile.value = values[lower_index];
            } else {
                double weight = index - lower_index;
                quantile.value = values[lower_index] * (1.0 - weight) + values[upper_index] * weight;
            }
        }
    }

    void start_collection_thread() {
        collection_thread_running_ = true;
        collection_thread_ = std::thread([this]() {
            auto logger = omop::common::Logger::get("omop-metrics");
            logger->info("Metrics collection thread started");
            
            while (collection_thread_running_) {
                std::unique_lock<std::mutex> lock(mutex_);
                collection_cv_.wait_for(lock, config_.collection_interval, [this]() {
                    return !collection_thread_running_.load();
                });
                
                if (!collection_thread_running_) {
                    break;
                }
                
                // Perform periodic cleanup
                auto cutoff_time = std::chrono::system_clock::now() - config_.retention_period;
                size_t deleted = delete_old_data(cutoff_time);
                
                if (deleted > 0) {
                    logger->debug("Deleted {} old metric values", deleted);
                }
            }
            
            logger->info("Metrics collection thread stopped");
        });
    }

    void stop_collection_thread() {
        collection_thread_running_ = false;
        collection_cv_.notify_all();
        
        if (collection_thread_.joinable()) {
            collection_thread_.join();
        }
    }

    std::string export_prometheus_format() {
        std::ostringstream oss;
        
        for (const auto& [name, metric] : metrics_) {
            oss << "# HELP " << name << " " << metric.definition.description << "\n";
            oss << "# TYPE " << name << " " << metric_type_to_string(metric.definition.type) << "\n";
            
            if (metric.definition.type == MetricType::Histogram && metric.histogram_data) {
                const auto& hist_data = metric.histogram_data.value();
                
                for (const auto& bucket : hist_data.buckets) {
                    oss << name << "_bucket{le=\"";
                    if (std::isinf(bucket.upper_bound)) {
                        oss << "+Inf";
                    } else {
                        oss << bucket.upper_bound;
                    }
                    oss << "\"} " << bucket.count << "\n";
                }
                
                oss << name << "_sum " << hist_data.sum << "\n";
                oss << name << "_count " << hist_data.total_count << "\n";
            } else if (metric.definition.type == MetricType::Summary && metric.summary_data) {
                const auto& summary_data = metric.summary_data.value();
                
                for (const auto& quantile : summary_data.quantiles) {
                    oss << name << "{quantile=\"" << quantile.quantile << "\"} " << quantile.value << "\n";
                }
                
                oss << name << "_sum " << summary_data.sum << "\n";
                oss << name << "_count " << summary_data.count << "\n";
            } else {
                for (const auto& value : metric.values) {
                    oss << name;
                    if (!value.labels.empty()) {
                        oss << "{";
                        bool first = true;
                        for (const auto& [key, val] : value.labels) {
                            if (!first) oss << ",";
                            oss << key << "=\"" << val << "\"";
                            first = false;
                        }
                        oss << "}";
                    }
                    oss << " " << value.value << "\n";
                }
            }
        }
        
        return oss.str();
    }

    std::string export_json_format() {
        std::ostringstream oss;
        oss << "{\n  \"metrics\": [\n";
        
        bool first_metric = true;
        for (const auto& [name, metric] : metrics_) {
            if (!first_metric) oss << ",\n";
            oss << "    {\n";
            oss << "      \"name\": \"" << name << "\",\n";
            oss << "      \"type\": \"" << metric_type_to_string(metric.definition.type) << "\",\n";
            oss << "      \"description\": \"" << metric.definition.description << "\",\n";
            oss << "      \"values\": [\n";
            
            bool first_value = true;
            for (const auto& value : metric.values) {
                if (!first_value) oss << ",\n";
                oss << "        {\n";
                oss << "          \"value\": " << value.value << ",\n";
                oss << "          \"timestamp\": " << std::chrono::duration_cast<std::chrono::milliseconds>(
                    value.timestamp.time_since_epoch()).count() << ",\n";
                oss << "          \"labels\": {\n";
                
                bool first_label = true;
                for (const auto& [key, val] : value.labels) {
                    if (!first_label) oss << ",\n";
                    oss << "            \"" << key << "\": \"" << val << "\"";
                    first_label = false;
                }
                
                oss << "\n          }\n";
                oss << "        }";
                first_value = false;
            }
            
            oss << "\n      ]\n";
            oss << "    }";
            first_metric = false;
        }
        
        oss << "\n  ]\n}";
        return oss.str();
    }
};

// MetricsCollector implementation
bool MetricsCollector::initialize(const MetricsConfig& config) {
    impl_ = std::make_unique<Impl>();
    return impl_->initialize(config);
}

bool MetricsCollector::register_metric(const MetricDefinition& definition) {
    return impl_->register_metric(definition);
}

bool MetricsCollector::increment_counter(const std::string& name, double value, 
                                       const std::unordered_map<std::string, std::string>& labels) {
    return impl_->increment_counter(name, value, labels);
}

bool MetricsCollector::set_gauge(const std::string& name, double value, 
                               const std::unordered_map<std::string, std::string>& labels) {
    return impl_->set_gauge(name, value, labels);
}

bool MetricsCollector::observe_histogram(const std::string& name, double value, 
                                       const std::unordered_map<std::string, std::string>& labels) {
    return impl_->observe_histogram(name, value, labels);
}

bool MetricsCollector::observe_summary(const std::string& name, double value, 
                                     const std::unordered_map<std::string, std::string>& labels) {
    return impl_->observe_summary(name, value, labels);
}

std::string MetricsCollector::start_timer(const std::string& name, 
                                        const std::unordered_map<std::string, std::string>& labels) {
    return impl_->start_timer(name, labels);
}

std::optional<double> MetricsCollector::stop_timer(const std::string& timer_id) {
    return impl_->stop_timer(timer_id);
}

std::optional<Metric> MetricsCollector::get_metric(const std::string& name) {
    return impl_->get_metric(name);
}

std::vector<Metric> MetricsCollector::query_metrics(const MetricsQuery& query) {
    return impl_->query_metrics(query);
}

std::vector<std::string> MetricsCollector::get_metric_names() {
    return impl_->get_metric_names();
}

std::string MetricsCollector::export_metrics(const std::string& format) {
    return impl_->export_metrics(format);
}

bool MetricsCollector::clear_metrics(const std::string& name) {
    return impl_->clear_metrics(name);
}

size_t MetricsCollector::delete_old_data(const std::chrono::system_clock::time_point& older_than) {
    return impl_->delete_old_data(older_than);
}

std::unordered_map<std::string, std::any> MetricsCollector::get_statistics() {
    return impl_->get_statistics();
}

MetricsConfig MetricsCollector::get_config() const {
    return impl_->get_config();
}

bool MetricsCollector::update_config(const MetricsConfig& config) {
    return impl_->update_config(config);
}

// Timer implementation
Timer::Timer(IMetricsCollector& collector, const std::string& metric_name,
             const std::unordered_map<std::string, std::string>& labels)
    : collector_(collector), stopped_(false) {
    timer_id_ = collector_.start_timer(metric_name, labels);
}

Timer::~Timer() {
    if (!stopped_) {
        stop();
    }
}

std::optional<double> Timer::stop() {
    if (stopped_) {
        return std::nullopt;
    }
    
    stopped_ = true;
    return collector_.stop_timer(timer_id_);
}

// Utility functions
std::unique_ptr<IMetricsCollector> create_metrics_collector() {
    return std::make_unique<MetricsCollector>();
}

MetricsConfig get_default_metrics_config() {
    MetricsConfig config;
    config.enabled = true;
    config.collection_interval = std::chrono::seconds(30);
    config.retention_period = std::chrono::seconds(86400); // 24 hours
    config.max_metrics = 10000;
    config.max_values_per_metric = 1000;
    config.export_format = "prometheus";
    config.export_endpoint = "/metrics";
    config.storage_backend = "memory";
    config.enable_compression = false;
    
    return config;
}

std::string metric_type_to_string(MetricType type) {
    switch (type) {
        case MetricType::Counter: return "counter";
        case MetricType::Gauge: return "gauge";
        case MetricType::Histogram: return "histogram";
        case MetricType::Summary: return "summary";
        case MetricType::Timer: return "timer";
        default: return "unknown";
    }
}

MetricType string_to_metric_type(const std::string& type_str) {
    if (type_str == "counter") return MetricType::Counter;
    if (type_str == "gauge") return MetricType::Gauge;
    if (type_str == "histogram") return MetricType::Histogram;
    if (type_str == "summary") return MetricType::Summary;
    if (type_str == "timer") return MetricType::Timer;
    return MetricType::Counter; // Default fallback
}

bool create_standard_etl_metrics(IMetricsCollector& collector) {
    auto logger = omop::common::Logger::get("omop-metrics");
    
    // Define standard ETL metrics
    std::vector<MetricDefinition> standard_metrics = {
        {
            "etl_records_processed_total",
            "Total number of records processed",
            MetricType::Counter,
            {"table", "operation", "status"},
            {},
            {},
            {}
        },
        {
            "etl_processing_duration_seconds",
            "Time spent processing records",
            MetricType::Histogram,
            {"table", "operation"},
            {0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0, 10.0, 30.0, 60.0},
            {},
            {}
        },
        {
            "etl_active_connections",
            "Number of active database connections",
            MetricType::Gauge,
            {"database", "type"},
            {},
            {},
            {}
        },
        {
            "etl_queue_size",
            "Number of items in processing queue",
            MetricType::Gauge,
            {"queue_type"},
            {},
            {},
            {}
        },
        {
            "etl_errors_total",
            "Total number of errors encountered",
            MetricType::Counter,
            {"table", "error_type", "severity"},
            {},
            {},
            {}
        },
        {
            "etl_memory_usage_bytes",
            "Current memory usage",
            MetricType::Gauge,
            {"component"},
            {},
            {},
            {}
        }
    };
    
    bool success = true;
    for (const auto& metric : standard_metrics) {
        if (!collector.register_metric(metric)) {
            logger->error("Failed to register standard metric: {}", metric.name);
            success = false;
        }
    }
    
    if (success) {
        logger->info("Successfully registered {} standard ETL metrics", standard_metrics.size());
    }
    
    return success;
}

} // namespace omop::monitoring