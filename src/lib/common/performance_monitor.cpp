/**
 * @file performance_monitor.cpp
 * @brief Implementation of performance monitoring and benchmarking utilities
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "performance_monitor.h"
#include <algorithm>
#include <numeric>
#include <sstream>
#include <fstream>
#include <iomanip>
#include <random>
#include <cmath>

#ifdef __linux__
#include <sys/resource.h>
#include <unistd.h>
#include <fstream>
#endif

#ifdef __APPLE__
#include <mach/mach.h>
#include <sys/resource.h>
#endif

#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#endif

namespace omop::common {

/**
 * @brief Implementation class for PerformanceMonitor
 */
class PerformanceMonitor::Impl {
private:
    struct TimerInfo {
        std::string metric_name;
        std::unordered_map<std::string, std::string> labels;
        std::chrono::high_resolution_clock::time_point start_time;
    };

public:
    Impl() = default;
    ~Impl() = default;

    bool initialize(const BenchmarkConfig& config) {
        config_ = config;
        is_initialized_ = true;
        return true;
    }

    bool start_monitoring() {
        if (!is_initialized_) {
            return false;
        }
        
        is_monitoring_ = true;
        start_time_ = std::chrono::high_resolution_clock::now();
        
        if (config_.collect_system_metrics) {
            start_system_monitoring();
        }
        
        return true;
    }

    bool stop_monitoring() {
        is_monitoring_ = false;
        
        if (system_monitor_thread_.joinable()) {
            system_monitor_thread_.join();
        }
        
        return true;
    }

    void record_measurement(const PerformanceMeasurement& measurement) {
        std::lock_guard<std::mutex> lock(measurements_mutex_);
        measurements_.push_back(measurement);
    }

    std::string start_timer(const std::string& metric_name,
                           const std::unordered_map<std::string, std::string>& labels) {
        std::string timer_id = generate_timer_id();
        
        TimerInfo timer;
        timer.metric_name = metric_name;
        timer.labels = labels;
        timer.start_time = std::chrono::high_resolution_clock::now();
        
        std::lock_guard<std::mutex> lock(timers_mutex_);
        active_timers_[timer_id] = timer;
        
        return timer_id;
    }

    std::optional<double> stop_timer(const std::string& timer_id) {
        auto end_time = std::chrono::high_resolution_clock::now();
        
        std::lock_guard<std::mutex> lock(timers_mutex_);
        auto it = active_timers_.find(timer_id);
        if (it == active_timers_.end()) {
            return std::nullopt;
        }
        
        auto& timer = it->second;
        auto duration = std::chrono::duration<double>(end_time - timer.start_time).count();
        
        // Record the measurement
        PerformanceMeasurement measurement;
        measurement.metric_name = timer.metric_name;
        measurement.type = PerformanceMetricType::Duration;
        measurement.value = duration;
        measurement.unit = "seconds";
        measurement.timestamp = std::chrono::system_clock::now();
        measurement.labels = timer.labels;
        
        record_measurement(measurement);
        
        active_timers_.erase(it);
        return duration;
    }

    BenchmarkResult run_benchmark(const std::string& benchmark_name,
                                 std::function<void()> test_func,
                                 const BenchmarkConfig& config) {
        BenchmarkResult result;
        result.benchmark_name = benchmark_name;
        
        // Warmup iterations
        for (size_t i = 0; i < config.warmup_iterations; ++i) {
            try {
                test_func();
            } catch (...) {
                // Ignore warmup failures
            }
        }
        
        // Actual benchmark iterations
        std::vector<std::chrono::duration<double>> iteration_times;
        iteration_times.reserve(config.iterations);
        
        auto benchmark_start = std::chrono::high_resolution_clock::now();
        
        for (size_t i = 0; i < config.iterations; ++i) {
            auto iter_start = std::chrono::high_resolution_clock::now();
            
            try {
                test_func();
                auto iter_end = std::chrono::high_resolution_clock::now();
                auto iter_duration = iter_end - iter_start;
                iteration_times.push_back(iter_duration);
                result.completed_iterations++;
            } catch (const std::exception& e) {
                result.failed_iterations++;
                if (result.failure_reason.empty()) {
                    result.failure_reason = e.what();
                }
            }
            
            // Check timeout
            auto current_time = std::chrono::high_resolution_clock::now();
            if (current_time - benchmark_start > config.timeout) {
                break;
            }
        }
        
        auto benchmark_end = std::chrono::high_resolution_clock::now();
        result.total_duration = benchmark_end - benchmark_start;
        
        // Calculate statistics
        if (!iteration_times.empty()) {
            result.success = true;
            result.average_duration = std::accumulate(iteration_times.begin(), iteration_times.end(),
                                                    std::chrono::duration<double>(0)) / iteration_times.size();
            result.min_duration = *std::min_element(iteration_times.begin(), iteration_times.end());
            result.max_duration = *std::max_element(iteration_times.begin(), iteration_times.end());
            
            // Calculate standard deviation
            double mean = result.average_duration.count();
            double variance = 0.0;
            for (const auto& duration : iteration_times) {
                double diff = duration.count() - mean;
                variance += diff * diff;
            }
            variance /= iteration_times.size();
            double std_dev = std::sqrt(variance);
            
            result.statistics["mean"] = mean;
            result.statistics["std_dev"] = std_dev;
            result.statistics["min"] = result.min_duration.count();
            result.statistics["max"] = result.max_duration.count();
        }
        
        return result;
    }

    SystemMetrics get_system_metrics() {
        SystemMetrics metrics;
        metrics.timestamp = std::chrono::system_clock::now();
        
#ifdef __linux__
        get_linux_system_metrics(metrics);
#elif defined(__APPLE__)
        get_macos_system_metrics(metrics);
#elif defined(_WIN32)
        get_windows_system_metrics(metrics);
#endif
        
        return metrics;
    }

    std::vector<PerformanceMeasurement> get_measurements(const std::string& metric_name) {
        std::lock_guard<std::mutex> lock(measurements_mutex_);
        
        if (metric_name.empty()) {
            return measurements_;
        }
        
        std::vector<PerformanceMeasurement> filtered;
        std::copy_if(measurements_.begin(), measurements_.end(), std::back_inserter(filtered),
                    [&metric_name](const PerformanceMeasurement& m) {
                        return m.metric_name == metric_name;
                    });
        
        return filtered;
    }

    std::unordered_map<std::string, double> calculate_statistics(const std::string& metric_name) {
        auto measurements = get_measurements(metric_name);
        std::unordered_map<std::string, double> stats;
        
        if (measurements.empty()) {
            return stats;
        }
        
        std::vector<double> values;
        values.reserve(measurements.size());
        for (const auto& m : measurements) {
            values.push_back(m.value);
        }
        
        std::sort(values.begin(), values.end());
        
        stats["count"] = static_cast<double>(values.size());
        stats["min"] = values.front();
        stats["max"] = values.back();
        stats["sum"] = std::accumulate(values.begin(), values.end(), 0.0);
        stats["average"] = stats["sum"] / stats["count"];
        
        // Percentiles
        stats["p50"] = percentile(values, 0.5);
        stats["p90"] = percentile(values, 0.9);
        stats["p95"] = percentile(values, 0.95);
        stats["p99"] = percentile(values, 0.99);
        
        // Standard deviation
        double mean = stats["average"];
        double variance = 0.0;
        for (double value : values) {
            double diff = value - mean;
            variance += diff * diff;
        }
        variance /= values.size();
        stats["std_dev"] = std::sqrt(variance);
        
        return stats;
    }

private:
    struct TimerInfo {
        std::string metric_name;
        std::unordered_map<std::string, std::string> labels;
        std::chrono::high_resolution_clock::time_point start_time;
    };

    BenchmarkConfig config_;
    bool is_initialized_{false};
    bool is_monitoring_{false};
    std::chrono::high_resolution_clock::time_point start_time_;
    
    std::vector<PerformanceMeasurement> measurements_;
    std::mutex measurements_mutex_;
    
    std::unordered_map<std::string, TimerInfo> active_timers_;
    std::mutex timers_mutex_;
    
    std::unordered_map<std::string, double> thresholds_;
    std::mutex thresholds_mutex_;
    
    std::thread system_monitor_thread_;
    std::atomic<bool> stop_system_monitoring_{false};

    std::string generate_timer_id() {
        static std::atomic<size_t> counter{0};
        return "timer_" + std::to_string(counter.fetch_add(1));
    }

    double percentile(const std::vector<double>& sorted_values, double p) {
        if (sorted_values.empty()) return 0.0;
        
        double index = p * (sorted_values.size() - 1);
        size_t lower = static_cast<size_t>(std::floor(index));
        size_t upper = static_cast<size_t>(std::ceil(index));
        
        if (lower == upper) {
            return sorted_values[lower];
        }
        
        double weight = index - lower;
        return sorted_values[lower] * (1.0 - weight) + sorted_values[upper] * weight;
    }

    void start_system_monitoring() {
        stop_system_monitoring_ = false;
        system_monitor_thread_ = std::thread([this]() {
            while (!stop_system_monitoring_ && is_monitoring_) {
                auto metrics = get_system_metrics();
                
                // Record system metrics as measurements
                record_system_metrics_as_measurements(metrics);
                
                std::this_thread::sleep_for(config_.sampling_interval);
            }
        });
    }

    void record_system_metrics_as_measurements(const SystemMetrics& metrics) {
        auto timestamp = std::chrono::system_clock::now();
        
        record_measurement({"cpu_usage", PerformanceMetricType::CPU, 
                           metrics.cpu_usage_percent, "percent", timestamp, {}, {}});
        record_measurement({"memory_usage", PerformanceMetricType::Memory, 
                           metrics.memory_usage_percent, "percent", timestamp, {}, {}});
        record_measurement({"memory_used", PerformanceMetricType::Memory, 
                           static_cast<double>(metrics.memory_used_bytes), "bytes", timestamp, {}, {}});
    }

#ifdef __linux__
    void get_linux_system_metrics(SystemMetrics& metrics) {
        // CPU usage
        std::ifstream stat_file("/proc/stat");
        if (stat_file.is_open()) {
            std::string line;
            std::getline(stat_file, line);
            // Parse CPU usage from /proc/stat
            // This is a simplified implementation
            metrics.cpu_usage_percent = 50.0; // Placeholder
        }
        
        // Memory usage
        std::ifstream meminfo("/proc/meminfo");
        if (meminfo.is_open()) {
            std::string line;
            size_t total_mem = 0, available_mem = 0;
            while (std::getline(meminfo, line)) {
                if (line.find("MemTotal:") == 0) {
                    sscanf(line.c_str(), "MemTotal: %zu kB", &total_mem);
                    total_mem *= 1024; // Convert to bytes
                } else if (line.find("MemAvailable:") == 0) {
                    sscanf(line.c_str(), "MemAvailable: %zu kB", &available_mem);
                    available_mem *= 1024; // Convert to bytes
                }
            }
            metrics.memory_available_bytes = available_mem;
            metrics.memory_used_bytes = total_mem - available_mem;
            if (total_mem > 0) {
                metrics.memory_usage_percent = 
                    (static_cast<double>(metrics.memory_used_bytes) / total_mem) * 100.0;
            }
        }
    }
#endif

#ifdef __APPLE__
    void get_macos_system_metrics(SystemMetrics& metrics) {
        // macOS-specific implementation using mach APIs
        mach_port_t host_port = mach_host_self();
        vm_size_t page_size;
        vm_statistics64_data_t vm_stat;
        mach_msg_type_number_t host_size = sizeof(vm_statistics64_data_t) / sizeof(natural_t);
        
        if (host_page_size(host_port, &page_size) == KERN_SUCCESS &&
            host_statistics64(host_port, HOST_VM_INFO64, (host_info64_t)&vm_stat, &host_size) == KERN_SUCCESS) {
            
            metrics.memory_used_bytes = (vm_stat.active_count + vm_stat.inactive_count + 
                                       vm_stat.wire_count) * page_size;
            metrics.memory_available_bytes = vm_stat.free_count * page_size;
            
            size_t total_memory = metrics.memory_used_bytes + metrics.memory_available_bytes;
            if (total_memory > 0) {
                metrics.memory_usage_percent = 
                    (static_cast<double>(metrics.memory_used_bytes) / total_memory) * 100.0;
            }
        }
        
        // CPU usage is more complex on macOS, using a simplified approach
        metrics.cpu_usage_percent = 25.0; // Placeholder
    }
#endif

#ifdef _WIN32
    void get_windows_system_metrics(SystemMetrics& metrics) {
        // Windows-specific implementation
        MEMORYSTATUSEX mem_status;
        mem_status.dwLength = sizeof(mem_status);
        if (GlobalMemoryStatusEx(&mem_status)) {
            metrics.memory_used_bytes = mem_status.ullTotalPhys - mem_status.ullAvailPhys;
            metrics.memory_available_bytes = mem_status.ullAvailPhys;
            metrics.memory_usage_percent = static_cast<double>(mem_status.dwMemoryLoad);
        }
        
        // CPU usage placeholder
        metrics.cpu_usage_percent = 30.0;
    }
#endif
};

// PerformanceMonitor implementation
bool PerformanceMonitor::initialize(const BenchmarkConfig& config) {
    if (!impl_) {
        impl_ = std::make_unique<Impl>();
    }
    return impl_->initialize(config);
}

bool PerformanceMonitor::start_monitoring() {
    return impl_ ? impl_->start_monitoring() : false;
}

bool PerformanceMonitor::stop_monitoring() {
    return impl_ ? impl_->stop_monitoring() : false;
}

void PerformanceMonitor::record_measurement(const PerformanceMeasurement& measurement) {
    if (impl_) {
        impl_->record_measurement(measurement);
    }
}

std::string PerformanceMonitor::start_timer(
    const std::string& metric_name,
    const std::unordered_map<std::string, std::string>& labels) {
    return impl_ ? impl_->start_timer(metric_name, labels) : "";
}

std::optional<double> PerformanceMonitor::stop_timer(const std::string& timer_id) {
    return impl_ ? impl_->stop_timer(timer_id) : std::nullopt;
}

BenchmarkResult PerformanceMonitor::run_benchmark(
    const std::string& benchmark_name,
    std::function<void()> test_func,
    const BenchmarkConfig& config) {
    return impl_ ? impl_->run_benchmark(benchmark_name, test_func, config) : BenchmarkResult{};
}

SystemMetrics PerformanceMonitor::get_system_metrics() {
    return impl_ ? impl_->get_system_metrics() : SystemMetrics{};
}

std::vector<PerformanceMeasurement> PerformanceMonitor::get_measurements(
    const std::string& metric_name) {
    return impl_ ? impl_->get_measurements(metric_name) : std::vector<PerformanceMeasurement>{};
}

std::unordered_map<std::string, double> PerformanceMonitor::calculate_statistics(
    const std::string& metric_name) {
    return impl_ ? impl_->calculate_statistics(metric_name) : std::unordered_map<std::string, double>{};
}

std::string PerformanceMonitor::generate_report(const std::string& format) {
    if (!impl_) {
        return "Performance monitor not initialized";
    }

    std::ostringstream report;

    if (format == "json") {
        report << "{\n";
        report << "  \"measurements\": [\n";

        auto measurements = impl_->get_measurements();
        for (size_t i = 0; i < measurements.size(); ++i) {
            const auto& m = measurements[i];
            report << "    {\n";
            report << "      \"metric_name\": \"" << m.metric_name << "\",\n";
            report << "      \"value\": " << m.value << ",\n";
            report << "      \"unit\": \"" << m.unit << "\"\n";
            report << "    }";
            if (i < measurements.size() - 1) report << ",";
            report << "\n";
        }

        report << "  ]\n";
        report << "}\n";
    } else {
        // Text format
        report << "Performance Report\n";
        report << "==================\n\n";

        auto measurements = impl_->get_measurements();
        std::unordered_map<std::string, std::vector<double>> metric_values;

        for (const auto& m : measurements) {
            metric_values[m.metric_name].push_back(m.value);
        }

        for (const auto& [metric_name, values] : metric_values) {
            auto stats = impl_->calculate_statistics(metric_name);
            report << "Metric: " << metric_name << "\n";
            report << "  Count: " << static_cast<size_t>(stats["count"]) << "\n";
            report << "  Average: " << std::fixed << std::setprecision(3) << stats["average"] << "\n";
            report << "  Min: " << stats["min"] << "\n";
            report << "  Max: " << stats["max"] << "\n";
            report << "  Std Dev: " << stats["std_dev"] << "\n";
            report << "  P95: " << stats["p95"] << "\n";
            report << "\n";
        }
    }

    return report.str();
}

bool PerformanceMonitor::export_measurements(const std::string& filename, const std::string& format) {
    if (!impl_) {
        return false;
    }

    std::ofstream file(filename);
    if (!file.is_open()) {
        return false;
    }

    auto measurements = impl_->get_measurements();

    if (format == "json") {
        file << "[\n";
        for (size_t i = 0; i < measurements.size(); ++i) {
            const auto& m = measurements[i];
            file << "  {\n";
            file << "    \"metric_name\": \"" << m.metric_name << "\",\n";
            file << "    \"value\": " << m.value << ",\n";
            file << "    \"unit\": \"" << m.unit << "\",\n";
            file << "    \"timestamp\": " << std::chrono::duration_cast<std::chrono::milliseconds>(
                m.timestamp.time_since_epoch()).count() << "\n";
            file << "  }";
            if (i < measurements.size() - 1) file << ",";
            file << "\n";
        }
        file << "]\n";
    } else {
        // CSV format
        file << "metric_name,value,unit,timestamp\n";
        for (const auto& m : measurements) {
            file << m.metric_name << "," << m.value << "," << m.unit << ","
                 << std::chrono::duration_cast<std::chrono::milliseconds>(
                    m.timestamp.time_since_epoch()).count() << "\n";
        }
    }

    return true;
}

void PerformanceMonitor::clear_measurements() {
    if (impl_) {
        // Implementation would clear measurements in Impl class
        // For now, we'll create a new Impl instance
        auto config = BenchmarkConfig{}; // Default config
        impl_ = std::make_unique<Impl>();
        impl_->initialize(config);
    }
}

void PerformanceMonitor::set_thresholds(const std::unordered_map<std::string, double>& thresholds) {
    // Implementation would store thresholds in Impl class
    // This is a placeholder implementation
}

std::vector<std::string> PerformanceMonitor::check_thresholds() {
    // Implementation would check thresholds against current measurements
    // This is a placeholder implementation
    return {};
}

// ScopedPerformanceTimer implementation
ScopedPerformanceTimer::ScopedPerformanceTimer(
    IPerformanceMonitor& monitor,
    const std::string& metric_name,
    const std::unordered_map<std::string, std::string>& labels)
    : monitor_(monitor), timer_id_(monitor.start_timer(metric_name, labels)) {
}

ScopedPerformanceTimer::~ScopedPerformanceTimer() {
    if (!stopped_) {
        monitor_.stop_timer(timer_id_);
    }
}

std::optional<double> ScopedPerformanceTimer::stop() {
    if (!stopped_) {
        stopped_ = true;
        return monitor_.stop_timer(timer_id_);
    }
    return std::nullopt;
}

// Factory functions
std::unique_ptr<IPerformanceMonitor> create_performance_monitor() {
    return std::make_unique<PerformanceMonitor>();
}

BenchmarkConfig get_default_benchmark_config() {
    BenchmarkConfig config;
    config.benchmark_name = "default_benchmark";
    config.iterations = 10;
    config.warmup_iterations = 2;
    config.timeout = std::chrono::seconds(60);
    config.collect_system_metrics = true;
    config.collect_memory_metrics = true;
    config.collect_cpu_metrics = true;
    config.sampling_interval = std::chrono::milliseconds(100);
    config.failure_threshold_pct = 10.0;
    return config;
}

} // namespace omop::common
