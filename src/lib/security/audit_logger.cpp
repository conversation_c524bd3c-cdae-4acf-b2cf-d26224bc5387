#include "audit_logger.h"
#include "common/logging.h"
#include <fstream>
#include <filesystem>
#include <sstream>
#include <iomanip>
#include <random>
#include <algorithm>
#include <chrono>
#include <mutex>
#include <deque>
#include <thread>
#include <condition_variable>
#include <regex>
#include <any>
#include <unordered_map>

#ifdef _WIN32
#include <windows.h>
#include <sddl.h>
#else
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>
#endif

namespace omop::security {

namespace {
    // Helper function to get current user
    std::string get_current_user() {
#ifdef _WIN32
        char username[256];
        DWORD username_len = sizeof(username);
        if (GetUserNameA(username, &username_len)) {
            return std::string(username);
        }
#else
        struct passwd* pw = getpwuid(getuid());
        if (pw) {
            return std::string(pw->pw_name);
        }
#endif
        return "unknown";
    }

    // Helper to format timestamp
    std::string format_timestamp(const std::chrono::system_clock::time_point& tp) {
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        std::tm tm;
#ifdef _WIN32
        localtime_s(&tm, &time_t);
#else
        localtime_r(&time_t, &tm);
#endif
        std::ostringstream oss;
        oss << std::put_time(&tm, "%d/%m/%Y %H:%M:%S");
        return oss.str();
    }

    // Helper to serialize event to JSON
    std::string serialize_event(const AuditEvent& event) {
        std::ostringstream json;
        json << "{"
             << "\"id\":\"" << event.id << "\","
             << "\"timestamp\":\"" << format_timestamp(event.timestamp) << "\","
             << "\"event_type\":\"" << event_type_to_string(event.event_type) << "\","
             << "\"severity\":\"" << severity_to_string(event.severity) << "\","
             << "\"outcome\":\"" << outcome_to_string(event.outcome) << "\","
             << "\"subject\":\"" << event.subject << "\","
             << "\"resource\":\"" << event.resource << "\","
             << "\"action\":\"" << event.action << "\","
             << "\"description\":\"" << event.description << "\","
             << "\"source_ip\":\"" << event.source_ip << "\","
             << "\"user_agent\":\"" << event.user_agent << "\","
             << "\"session_id\":\"" << event.session_id << "\","
             << "\"request_id\":\"" << event.request_id << "\"";
        
        // Add context if present
        if (!event.context.empty()) {
            json << ",\"context\":{";
            bool first = true;
            for (const auto& [key, value] : event.context) {
                if (!first) json << ",";
                json << "\"" << key << "\":\"";
                // Simple any_cast to string - in production would need proper type handling
                try {
                    json << std::any_cast<std::string>(value);
                } catch(...) {
                    json << "complex_value";
                }
                json << "\"";
                first = false;
            }
            json << "}";
        }
        
        json << "}";
        return json.str();
    }

    // Helper to escape JSON strings
    std::string escape_json_string(const std::string& str) {
        std::string result;
        result.reserve(str.length());
        
        for (char c : str) {
            switch (c) {
                case '"': result += "\\\""; break;
                case '\\': result += "\\\\"; break;
                case '\b': result += "\\b"; break;
                case '\f': result += "\\f"; break;
                case '\n': result += "\\n"; break;
                case '\r': result += "\\r"; break;
                case '\t': result += "\\t"; break;
                default:
                    if (c < 32) {
                        std::ostringstream oss;
                        oss << "\\u" << std::hex << std::setw(4) << std::setfill('0') << static_cast<int>(c);
                        result += oss.str();
                    } else {
                        result += c;
                    }
                    break;
            }
        }
        return result;
    }

    // Helper to generate unique event ID
    std::string generate_event_id() {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::uniform_int_distribution<> dis(0, 999999);
        
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()).count();
        
        std::ostringstream oss;
        oss << "audit_" << timestamp << "_" << dis(gen);
        return oss.str();
    }

    // Helper to rotate log file
    bool rotate_log_file(const std::string& log_path, size_t max_size) {
        try {
            if (!std::filesystem::exists(log_path)) {
                return true;
            }
            
            auto file_size = std::filesystem::file_size(log_path);
            if (file_size < max_size) {
                return true;
            }
            
            // Create backup filename with timestamp
            auto now = std::chrono::system_clock::now();
            auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(
                now.time_since_epoch()).count();
            
            std::filesystem::path log_file(log_path);
            std::filesystem::path backup_path = log_file.parent_path() / 
                (log_file.stem().string() + "_" + std::to_string(timestamp) + 
                 log_file.extension().string());
            
            std::filesystem::rename(log_path, backup_path);
            return true;
        } catch (const std::exception& e) {
            spdlog::error("Failed to rotate log file: {}", e.what());
            return false;
        }
    }
}

// PIMPL implementation for AuditLogger
class AuditLogger::Impl {
public:
    Impl() = default;
    ~Impl() {
        stop_background_thread();
    }
    
    AuditConfig config_;
    bool initialized_ = false;
    std::shared_ptr<spdlog::logger> logger_;
    std::ofstream log_file_;
    std::mutex log_mutex_;
    std::deque<AuditEvent> event_buffer_;
    std::thread background_thread_;
    std::condition_variable cv_;
    bool stop_thread_ = false;
    
    bool initialize(const AuditConfig& config) {
        std::lock_guard<std::mutex> lock(log_mutex_);
        
        config_ = config;
        
        if (!config_.enabled) {
            initialized_ = true;
            return true;
        }
        
        try {
            // Create log directory if it doesn't exist
            std::filesystem::path log_path(config_.log_file_path);
            std::filesystem::create_directories(log_path.parent_path());
            
            // Open log file
            log_file_.open(config_.log_file_path, std::ios::app);
            if (!log_file_.is_open()) {
                spdlog::error("Failed to open audit log file: {}", config_.log_file_path);
                return false;
            }
            
            // Initialize logger
            logger_ = common::Logger::get("audit_logger");
            logger_->info("AuditLogger initialized with file: {}", config_.log_file_path);
            
            // Start background thread for flushing
            start_background_thread();
            
            initialized_ = true;
            return true;
        } catch (const std::exception& e) {
            spdlog::error("Failed to initialize AuditLogger: {}", e.what());
            return false;
        }
    }
    
    bool log_event(const AuditEvent& event) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(log_mutex_);
        
        try {
            // Add event to buffer
            event_buffer_.push_back(event);
            
            // Flush immediately if buffer is full
            if (event_buffer_.size() >= config_.buffer_size) {
                flush_buffer();
            }
            
            return true;
        } catch (const std::exception& e) {
            spdlog::error("Failed to log audit event: {}", e.what());
            return false;
        }
    }
    
    void flush_buffer() {
        if (event_buffer_.empty() || !log_file_.is_open()) {
            return;
        }
        
        try {
            // Check if rotation is needed
            if (log_file_.tellp() > static_cast<std::streampos>(config_.max_file_size)) {
                log_file_.close();
                rotate_log_file(config_.log_file_path, config_.max_file_size);
                log_file_.open(config_.log_file_path, std::ios::app);
            }
            
            // Write events to file
            for (const auto& event : event_buffer_) {
                std::string json_event = serialize_event(event);
                log_file_ << json_event << std::endl;
            }
            
            log_file_.flush();
            event_buffer_.clear();
            
        } catch (const std::exception& e) {
            spdlog::error("Failed to flush audit buffer: {}", e.what());
        }
    }
    
    void start_background_thread() {
        stop_thread_ = false;
        background_thread_ = std::thread([this]() {
            while (!stop_thread_) {
                std::unique_lock<std::mutex> lock(log_mutex_);
                if (cv_.wait_for(lock, config_.flush_interval, [this]() { return stop_thread_; })) {
                    break;
                }
                
                if (!event_buffer_.empty()) {
                    flush_buffer();
                }
            }
        });
    }
    
    void stop_background_thread() {
        {
            std::lock_guard<std::mutex> lock(log_mutex_);
            stop_thread_ = true;
        }
        cv_.notify_all();
        
        if (background_thread_.joinable()) {
            background_thread_.join();
        }
    }
    
    std::vector<AuditEvent> get_events(
        const std::chrono::system_clock::time_point& start_time,
        const std::chrono::system_clock::time_point& end_time,
        const std::string& event_type,
        const std::string& user_id) {
        
        std::vector<AuditEvent> events;
        
        if (!std::filesystem::exists(config_.log_file_path)) {
            return events;
        }
        
        try {
            std::ifstream file(config_.log_file_path);
            std::string line;
            
            while (std::getline(file, line)) {
                // Simple parsing - in production would use proper JSON parser
                if (line.find("\"timestamp\"") != std::string::npos) {
                    // Extract timestamp and check if it's in range
                    // This is a simplified implementation
                    AuditEvent event;
                    // Parse event from JSON line (simplified)
                    events.push_back(event);
                }
            }
        } catch (const std::exception& e) {
            spdlog::error("Failed to read audit events: {}", e.what());
        }
        
        return events;
    }
    
    bool archive_events(const std::chrono::system_clock::time_point& before_time) {
        // Implementation for archiving old events
        // This would typically move events to a separate archive file or database
        return true;
    }
    
    std::unordered_map<std::string, std::any> get_statistics() {
        std::lock_guard<std::mutex> lock(log_mutex_);
        
        std::unordered_map<std::string, std::any> stats;
        stats["total_events"] = static_cast<int>(event_buffer_.size());
        stats["buffer_size"] = static_cast<int>(config_.buffer_size);
        stats["max_file_size"] = static_cast<int>(config_.max_file_size);
        stats["flush_interval_ms"] = static_cast<int>(
            std::chrono::duration_cast<std::chrono::milliseconds>(config_.flush_interval).count());
        
        return stats;
    }
};

AuditLogger::AuditLogger() : impl_(std::make_unique<Impl>()) {}

AuditLogger::~AuditLogger() = default;

bool AuditLogger::initialize(const AuditConfig& config) {
    return impl_->initialize(config);
}

bool AuditLogger::log_event(const AuditEvent& event) {
    return impl_->log_event(event);
}

bool AuditLogger::log_authentication(
    const std::string& user_id,
    const std::string& action,
    bool success,
    const std::string& details) {
    
    AuditEvent event;
    event.id = generate_event_id();
    event.timestamp = std::chrono::system_clock::now();
    event.event_type = AuditEventType::Authentication;
    event.severity = success ? AuditSeverity::Low : AuditSeverity::High;
    event.outcome = success ? AuditOutcome::Success : AuditOutcome::Failure;
    event.subject = user_id;
    event.action = action;
    event.description = details;
    event.source_ip = "127.0.0.1"; // Would be extracted from request context
    event.user_agent = "OMOP-ETL"; // Would be extracted from request context
    
    return log_event(event);
}

bool AuditLogger::log_data_access(
    const std::string& table_name,
    const std::string& operation,
    size_t record_count,
    const std::string& user_id) {
    
    AuditEvent event;
    event.id = generate_event_id();
    event.timestamp = std::chrono::system_clock::now();
    event.event_type = AuditEventType::DataAccess;
    event.severity = AuditSeverity::Medium;
    event.outcome = AuditOutcome::Success;
    event.subject = user_id;
    event.resource = table_name;
    event.action = operation;
    event.description = "Data access: " + std::to_string(record_count) + " records";
    event.source_ip = "127.0.0.1";
    event.user_agent = "OMOP-ETL";
    
    return log_event(event);
}

bool AuditLogger::log_configuration_change(
    const std::string& component,
    const std::string& setting,
    const std::string& old_value,
    const std::string& new_value,
    const std::string& user_id) {
    
    AuditEvent event;
    event.id = generate_event_id();
    event.timestamp = std::chrono::system_clock::now();
    event.event_type = AuditEventType::ConfigurationChange;
    event.severity = AuditSeverity::Medium;
    event.outcome = AuditOutcome::Success;
    event.subject = user_id;
    event.resource = component;
    event.action = "configuration_change";
    event.description = "Setting changed: " + setting + " from '" + old_value + "' to '" + new_value + "'";
    event.source_ip = "127.0.0.1";
    event.user_agent = "OMOP-ETL";
    
    return log_event(event);
}

bool AuditLogger::log_job_execution(
    const std::string& job_id,
    const std::string& action,
    const std::string& status,
    const std::string& user_id) {
    
    AuditEvent event;
    event.id = generate_event_id();
    event.timestamp = std::chrono::system_clock::now();
    event.event_type = AuditEventType::JobExecution;
    event.severity = status == "success" ? AuditSeverity::Low : AuditSeverity::Medium;
    event.outcome = status == "success" ? AuditOutcome::Success : AuditOutcome::Failure;
    event.subject = user_id;
    event.resource = job_id;
    event.action = action;
    event.description = "Job execution: " + status;
    event.source_ip = "127.0.0.1";
    event.user_agent = "OMOP-ETL";
    
    return log_event(event);
}

std::vector<AuditEvent> AuditLogger::get_events(
    const std::chrono::system_clock::time_point& start_time,
    const std::chrono::system_clock::time_point& end_time,
    const std::string& event_type,
    const std::string& user_id) {
    return impl_->get_events(start_time, end_time, event_type, user_id);
}

bool AuditLogger::archive_events(
    const std::chrono::system_clock::time_point& before_time) {
    return impl_->archive_events(before_time);
}

std::unordered_map<std::string, std::any> AuditLogger::get_statistics() {
    return impl_->get_statistics();
}

AuditConfig AuditLogger::get_config() const {
    return impl_->config_;
}

bool AuditLogger::update_config(const AuditConfig& config) {
    return impl_->initialize(config);
}

} // namespace omop::security