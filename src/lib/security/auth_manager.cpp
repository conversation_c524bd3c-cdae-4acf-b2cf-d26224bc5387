#include "auth_manager.h"
#include "common/logging.h"
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <random>
#include <regex>
#include <mutex>
#include <unordered_set>

// For password hashing
#ifdef _WIN32
#include <windows.h>
#include <bcrypt.h>
#pragma comment(lib, "bcrypt.lib")
#else
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/sha.h>
#endif

namespace omop::security {

namespace {
    // Constants for token generation
    constexpr size_t TOKEN_LENGTH = 32;
    constexpr size_t SALT_LENGTH = 16;
    constexpr int PBKDF2_ITERATIONS = 100000;
    
    // Helper to generate secure random bytes
    std::string generate_random_bytes(size_t length) {
        std::string result(length, '\0');
        
#ifdef _WIN32
        BCRYPT_ALG_HANDLE hAlgorithm;
        if (BCryptOpenAlgorithmProvider(&hAlgorithm, BCRYPT_RNG_ALGORITHM, nullptr, 0) == 0) {
            BCryptGenRandom(hAlgorithm, reinterpret_cast<PUCHAR>(&result[0]), 
                          static_cast<ULONG>(length), 0);
            BCryptCloseAlgorithmProvider(hAlgorithm, 0);
        }
#else
        RAND_bytes(reinterpret_cast<unsigned char*>(&result[0]), length);
#endif
        
        return result;
    }
    
    // Convert bytes to hex string
    std::string bytes_to_hex(const std::string& bytes) {
        std::stringstream ss;
        ss << std::hex << std::setfill('0');
        for (unsigned char c : bytes) {
            ss << std::setw(2) << static_cast<int>(c);
        }
        return ss.str();
    }
    
    // Convert hex string to bytes
    std::string hex_to_bytes(const std::string& hex) {
        std::string bytes;
        for (size_t i = 0; i < hex.length(); i += 2) {
            std::string byte_string = hex.substr(i, 2);
            char byte = static_cast<char>(std::strtol(byte_string.c_str(), nullptr, 16));
            bytes.push_back(byte);
        }
        return bytes;
    }
    
    // Simple JWT-like token generation (for demonstration)
    std::string create_jwt_token(const std::string& subject, 
                                const std::vector<std::string>& scopes,
                                std::chrono::seconds lifetime) {
        // In production, would use proper JWT library
        auto now = std::chrono::system_clock::now();
        auto exp = now + lifetime;
        
        std::stringstream token;
        token << "omop_token_v1.";
        token << subject << ".";
        token << std::chrono::duration_cast<std::chrono::seconds>(exp.time_since_epoch()).count() << ".";
        
        for (const auto& scope : scopes) {
            token << scope << ",";
        }
        token << ".";
        token << generate_random_bytes(16); // Random signature
        
        return token.str();
    }
    
    // Extract subject from token
    std::string extract_subject_from_token(const std::string& token) {
        size_t first_dot = token.find('.');
        if (first_dot == std::string::npos) return "";
        
        size_t second_dot = token.find('.', first_dot + 1);
        if (second_dot == std::string::npos) return "";
        
        return token.substr(first_dot + 1, second_dot - first_dot - 1);
    }
    
    // Hash password using PBKDF2
    std::string hash_password(const std::string& password, const std::string& salt) {
#ifdef _WIN32
        // Windows implementation using BCrypt
        BCRYPT_ALG_HANDLE hAlgorithm;
        if (BCryptOpenAlgorithmProvider(&hAlgorithm, BCRYPT_SHA512_ALGORITHM, nullptr, 0) != 0) {
            return "";
        }
        
        BCRYPT_KEY_HANDLE hKey;
        if (BCryptDeriveKeyPBKDF2(hAlgorithm, 
                                 reinterpret_cast<PUCHAR>(const_cast<char*>(password.c_str())),
                                 static_cast<ULONG>(password.length()),
                                 reinterpret_cast<PUCHAR>(const_cast<char*>(salt.c_str())),
                                 static_cast<ULONG>(salt.length()),
                                 PBKDF2_ITERATIONS,
                                 nullptr, 0, 0) != 0) {
            BCryptCloseAlgorithmProvider(hAlgorithm, 0);
            return "";
        }
        
        ULONG hash_length = 64; // SHA-512 hash length
        std::string hash(hash_length, '\0');
        
        if (BCryptDeriveKeyPBKDF2(hAlgorithm, 
                                 reinterpret_cast<PUCHAR>(const_cast<char*>(password.c_str())),
                                 static_cast<ULONG>(password.length()),
                                 reinterpret_cast<PUCHAR>(const_cast<char*>(salt.c_str())),
                                 static_cast<ULONG>(salt.length()),
                                 PBKDF2_ITERATIONS,
                                 reinterpret_cast<PUCHAR>(&hash[0]),
                                 hash_length, 0) != 0) {
            BCryptCloseAlgorithmProvider(hAlgorithm, 0);
            return "";
        }
        
        BCryptCloseAlgorithmProvider(hAlgorithm, 0);
        return hash;
#else
        // Unix implementation using OpenSSL
        std::string hash(64, '\0'); // SHA-512 hash length
        
        if (PKCS5_PBKDF2_HMAC(password.c_str(), password.length(),
                             reinterpret_cast<const unsigned char*>(salt.c_str()), salt.length(),
                             PBKDF2_ITERATIONS, EVP_sha512(),
                             hash.length(), reinterpret_cast<unsigned char*>(&hash[0])) != 1) {
            return "";
        }
        
        return hash;
#endif
    }
    
    // Verify password against hash
    bool verify_password(const std::string& password, const std::string& stored_hash, const std::string& salt) {
        std::string computed_hash = hash_password(password, salt);
        return computed_hash == stored_hash;
    }
    
    // Generate secure token
    std::string generate_token(size_t length = TOKEN_LENGTH) {
        return bytes_to_hex(generate_random_bytes(length));
    }
    
    // Validate password strength
    bool is_password_strong(const std::string& password) {
        if (password.length() < 8) return false;
        
        bool has_upper = false, has_lower = false, has_digit = false, has_special = false;
        
        for (char c : password) {
            if (std::isupper(c)) has_upper = true;
            else if (std::islower(c)) has_lower = true;
            else if (std::isdigit(c)) has_digit = true;
            else has_special = true;
        }
        
        return has_upper && has_lower && has_digit && has_special;
    }
}

// PIMPL implementation for AuthManager
class AuthManager::Impl {
public:
    Impl() = default;
    ~Impl() = default;
    
    AuthConfig config_;
    bool initialized_ = false;
    std::shared_ptr<spdlog::logger> logger_;
    std::mutex auth_mutex_;
    
    // In-memory storage (in production, would use database)
    std::unordered_map<std::string, UserInfo> users_;
    std::unordered_map<std::string, std::string> password_hashes_;
    std::unordered_map<std::string, std::string> password_salts_;
    std::unordered_map<std::string, AuthToken> active_tokens_;
    std::unordered_map<std::string, std::vector<std::string>> user_sessions_;
    std::unordered_set<std::string> revoked_tokens_;
    
    bool initialize(const AuthConfig& config) {
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        config_ = config;
        
        if (!config_.enabled) {
            initialized_ = true;
            return true;
        }
        
        try {
            logger_ = common::Logger::get("auth_manager");
            logger_->info("AuthManager initialized");
            
            // Initialize with default admin user if configured
            if (config_.create_default_admin) {
                create_default_admin_user();
            }
            
            initialized_ = true;
            return true;
        } catch (const std::exception& e) {
            spdlog::error("Failed to initialize AuthManager: {}", e.what());
            return false;
        }
    }
    
    void create_default_admin_user() {
        UserInfo admin_user;
        admin_user.user_id = "admin";
        admin_user.username = "admin";
        admin_user.email = "<EMAIL>";
        admin_user.roles = {"admin"};
        admin_user.status = UserStatus::Active;
        admin_user.created_at = std::chrono::system_clock::now();
        
        std::string salt = generate_random_bytes(SALT_LENGTH);
        std::string hash = hash_password("admin123", salt);
        
        users_["admin"] = admin_user;
        password_hashes_["admin"] = hash;
        password_salts_["admin"] = salt;
        
        logger_->info("Created default admin user");
    }
    
    std::pair<AuthResult, std::optional<AuthToken>> authenticate(const AuthCredentials& credentials) {
        if (!initialized_ || !config_.enabled) {
            return {AuthResult::SystemError, std::nullopt};
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        try {
            // Find user by username
            auto user_it = std::find_if(users_.begin(), users_.end(),
                [&](const auto& pair) { return pair.second.username == credentials.username; });
            
            if (user_it == users_.end()) {
                logger_->warn("Authentication failed: user not found: {}", credentials.username);
                return {AuthResult::InvalidCredentials, std::nullopt};
            }
            
            const auto& user = user_it->second;
            
            // Check if user is locked
            if (user.status == UserStatus::Locked) {
                logger_->warn("Authentication failed: user locked: {}", credentials.username);
                return {AuthResult::AccountLocked, std::nullopt};
            }
            
            // Verify password
            auto hash_it = password_hashes_.find(user.user_id);
            auto salt_it = password_salts_.find(user.user_id);
            
            if (hash_it == password_hashes_.end() || salt_it == password_salts_.end()) {
                logger_->error("User password data not found: {}", user.user_id);
                return {AuthResult::SystemError, std::nullopt};
            }
            
            if (!verify_password(credentials.password, hash_it->second, salt_it->second)) {
                logger_->warn("Authentication failed: invalid password for user: {}", credentials.username);
                return {AuthResult::InvalidCredentials, std::nullopt};
            }
            
            // Generate token
            AuthToken token;
            token.token = generate_token();
            token.token_type = "Bearer";
            token.issued_at = std::chrono::system_clock::now();
            token.expires_at = token.issued_at + config_.token_lifetime;
            token.issuer = "omop-etl";
            token.subject = user.user_id;
            token.scopes = user.roles;
            
            // Store token
            active_tokens_[token.token] = token;
            user_sessions_[user.user_id].push_back(token.token);
            
            logger_->info("Authentication successful: {}", credentials.username);
            return {AuthResult::Success, token};
            
        } catch (const std::exception& e) {
            logger_->error("Authentication error: {}", e.what());
            return {AuthResult::SystemError, std::nullopt};
        }
    }
    
    std::pair<AuthResult, std::optional<UserInfo>> validate_token(const std::string& token) {
        if (!initialized_ || !config_.enabled) {
            return {AuthResult::SystemError, std::nullopt};
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        try {
            // Check if token is revoked
            if (revoked_tokens_.find(token) != revoked_tokens_.end()) {
                return {AuthResult::TokenRevoked, std::nullopt};
            }
            
            // Find token
            auto token_it = active_tokens_.find(token);
            if (token_it == active_tokens_.end()) {
                return {AuthResult::InvalidToken, std::nullopt};
            }
            
            const auto& auth_token = token_it->second;
            
            // Check if token is expired
            if (std::chrono::system_clock::now() > auth_token.expires_at) {
                active_tokens_.erase(token_it);
                return {AuthResult::TokenExpired, std::nullopt};
            }
            
            // Find user
            auto user_it = users_.find(auth_token.subject);
            if (user_it == users_.end()) {
                return {AuthResult::UserNotFound, std::nullopt};
            }
            
            const auto& user = user_it->second;
            
            // Check if user is still active
            if (user.status != UserStatus::Active) {
                return {AuthResult::AccountLocked, std::nullopt};
            }
            
            return {AuthResult::Success, user};
            
        } catch (const std::exception& e) {
            logger_->error("Token validation error: {}", e.what());
            return {AuthResult::SystemError, std::nullopt};
        }
    }
    
    std::pair<AuthResult, std::optional<AuthToken>> refresh_token(const std::string& refresh_token) {
        if (!initialized_ || !config_.enabled) {
            return {AuthResult::SystemError, std::nullopt};
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        try {
            // Validate current token
            auto validation_result = validate_token(refresh_token);
            if (validation_result.first != AuthResult::Success) {
                return {validation_result.first, std::nullopt};
            }
            
            const auto& user = validation_result.second.value();
            
            // Generate new token
            AuthToken new_token;
            new_token.token = generate_token();
            new_token.token_type = "Bearer";
            new_token.issued_at = std::chrono::system_clock::now();
            new_token.expires_at = new_token.issued_at + config_.token_lifetime;
            new_token.issuer = "omop-etl";
            new_token.subject = user.user_id;
            new_token.scopes = user.roles;
            
            // Revoke old token and store new one
            revoked_tokens_.insert(refresh_token);
            active_tokens_.erase(refresh_token);
            active_tokens_[new_token.token] = new_token;
            
            // Update user sessions
            auto& sessions = user_sessions_[user.user_id];
            sessions.erase(std::remove(sessions.begin(), sessions.end(), refresh_token), sessions.end());
            sessions.push_back(new_token.token);
            
            logger_->info("Token refreshed for user: {}", user.username);
            return {AuthResult::Success, new_token};
            
        } catch (const std::exception& e) {
            logger_->error("Token refresh error: {}", e.what());
            return {AuthResult::SystemError, std::nullopt};
        }
    }
    
    bool revoke_token(const std::string& token) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        try {
            auto token_it = active_tokens_.find(token);
            if (token_it == active_tokens_.end()) {
                return false;
            }
            
            const auto& auth_token = token_it->second;
            
            // Add to revoked tokens
            revoked_tokens_.insert(token);
            
            // Remove from active tokens
            active_tokens_.erase(token_it);
            
            // Remove from user sessions
            auto& sessions = user_sessions_[auth_token.subject];
            sessions.erase(std::remove(sessions.begin(), sessions.end(), token), sessions.end());
            
            logger_->info("Token revoked for user: {}", auth_token.subject);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Token revocation error: {}", e.what());
            return false;
        }
    }
    
    std::optional<UserInfo> get_user_info(const std::string& user_id) {
        if (!initialized_ || !config_.enabled) {
            return std::nullopt;
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        auto it = users_.find(user_id);
        if (it != users_.end()) {
            return it->second;
        }
        
        return std::nullopt;
    }
    
    bool create_user(const UserInfo& user_info, const std::string& password) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        try {
            // Check if user already exists
            if (users_.find(user_info.user_id) != users_.end()) {
                logger_->warn("User creation failed: user already exists: {}", user_info.user_id);
                return false;
            }
            
            // Validate password strength
            if (!is_password_strong(password)) {
                logger_->warn("User creation failed: weak password for user: {}", user_info.user_id);
                return false;
            }
            
            // Generate salt and hash password
            std::string salt = generate_random_bytes(SALT_LENGTH);
            std::string hash = hash_password(password, salt);
            
            // Store user and password data
            users_[user_info.user_id] = user_info;
            password_hashes_[user_info.user_id] = hash;
            password_salts_[user_info.user_id] = salt;
            
            logger_->info("User created: {}", user_info.user_id);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("User creation error: {}", e.what());
            return false;
        }
    }
    
    bool update_user(const UserInfo& user_info) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        try {
            auto it = users_.find(user_info.user_id);
            if (it == users_.end()) {
                logger_->warn("User update failed: user not found: {}", user_info.user_id);
                return false;
            }
            
            users_[user_info.user_id] = user_info;
            logger_->info("User updated: {}", user_info.user_id);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("User update error: {}", e.what());
            return false;
        }
    }
    
    bool delete_user(const std::string& user_id) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        try {
            // Revoke all user sessions
            terminate_all_sessions(user_id);
            
            // Remove user data
            users_.erase(user_id);
            password_hashes_.erase(user_id);
            password_salts_.erase(user_id);
            user_sessions_.erase(user_id);
            
            logger_->info("User deleted: {}", user_id);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("User deletion error: {}", e.what());
            return false;
        }
    }
    
    bool lock_user(const std::string& user_id) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        try {
            auto it = users_.find(user_id);
            if (it == users_.end()) {
                return false;
            }
            
            it->second.status = UserStatus::Locked;
            logger_->info("User locked: {}", user_id);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("User lock error: {}", e.what());
            return false;
        }
    }
    
    bool unlock_user(const std::string& user_id) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        try {
            auto it = users_.find(user_id);
            if (it == users_.end()) {
                return false;
            }
            
            it->second.status = UserStatus::Active;
            logger_->info("User unlocked: {}", user_id);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("User unlock error: {}", e.what());
            return false;
        }
    }
    
    bool change_password(const std::string& user_id, const std::string& old_password, const std::string& new_password) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        try {
            // Verify old password
            auto hash_it = password_hashes_.find(user_id);
            auto salt_it = password_salts_.find(user_id);
            
            if (hash_it == password_hashes_.end() || salt_it == password_salts_.end()) {
                return false;
            }
            
            if (!verify_password(old_password, hash_it->second, salt_it->second)) {
                logger_->warn("Password change failed: invalid old password for user: {}", user_id);
                return false;
            }
            
            // Validate new password strength
            if (!is_password_strong(new_password)) {
                logger_->warn("Password change failed: weak new password for user: {}", user_id);
                return false;
            }
            
            // Generate new salt and hash
            std::string new_salt = generate_random_bytes(SALT_LENGTH);
            std::string new_hash = hash_password(new_password, new_salt);
            
            // Update password data
            password_hashes_[user_id] = new_hash;
            password_salts_[user_id] = new_salt;
            
            logger_->info("Password changed for user: {}", user_id);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Password change error: {}", e.what());
            return false;
        }
    }
    
    bool reset_password(const std::string& user_id, const std::string& new_password) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        try {
            // Validate new password strength
            if (!is_password_strong(new_password)) {
                logger_->warn("Password reset failed: weak password for user: {}", user_id);
                return false;
            }
            
            // Generate new salt and hash
            std::string new_salt = generate_random_bytes(SALT_LENGTH);
            std::string new_hash = hash_password(new_password, new_salt);
            
            // Update password data
            password_hashes_[user_id] = new_hash;
            password_salts_[user_id] = new_salt;
            
            logger_->info("Password reset for user: {}", user_id);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Password reset error: {}", e.what());
            return false;
        }
    }
    
    std::vector<std::string> get_active_sessions(const std::string& user_id) {
        if (!initialized_ || !config_.enabled) {
            return {};
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        auto it = user_sessions_.find(user_id);
        if (it != user_sessions_.end()) {
            return it->second;
        }
        
        return {};
    }
    
    bool terminate_session(const std::string& user_id, const std::string& session_token) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        try {
            auto& sessions = user_sessions_[user_id];
            auto it = std::find(sessions.begin(), sessions.end(), session_token);
            
            if (it != sessions.end()) {
                revoked_tokens_.insert(session_token);
                active_tokens_.erase(session_token);
                sessions.erase(it);
                
                logger_->info("Session terminated for user: {}", user_id);
                return true;
            }
            
            return false;
            
        } catch (const std::exception& e) {
            logger_->error("Session termination error: {}", e.what());
            return false;
        }
    }
    
    bool terminate_all_sessions(const std::string& user_id) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        try {
            auto& sessions = user_sessions_[user_id];
            
            for (const auto& session_token : sessions) {
                revoked_tokens_.insert(session_token);
                active_tokens_.erase(session_token);
            }
            
            sessions.clear();
            
            logger_->info("All sessions terminated for user: {}", user_id);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Session termination error: {}", e.what());
            return false;
        }
    }
    
    std::unordered_map<std::string, std::any> get_statistics() {
        std::lock_guard<std::mutex> lock(auth_mutex_);
        
        std::unordered_map<std::string, std::any> stats;
        stats["total_users"] = static_cast<int>(users_.size());
        stats["active_tokens"] = static_cast<int>(active_tokens_.size());
        stats["revoked_tokens"] = static_cast<int>(revoked_tokens_.size());
        
        int active_sessions = 0;
        for (const auto& [user_id, sessions] : user_sessions_) {
            active_sessions += static_cast<int>(sessions.size());
        }
        stats["active_sessions"] = active_sessions;
        
        return stats;
    }
};

AuthManager::AuthManager() : impl_(std::make_unique<Impl>()) {}

AuthManager::~AuthManager() = default;

bool AuthManager::initialize(const AuthConfig& config) {
    return impl_->initialize(config);
}

std::pair<AuthResult, std::optional<AuthToken>> AuthManager::authenticate(
    const AuthCredentials& credentials) {
    return impl_->authenticate(credentials);
}

std::pair<AuthResult, std::optional<UserInfo>> AuthManager::validate_token(
    const std::string& token) {
    return impl_->validate_token(token);
}

std::pair<AuthResult, std::optional<AuthToken>> AuthManager::refresh_token(
    const std::string& refresh_token) {
    return impl_->refresh_token(refresh_token);
}

bool AuthManager::revoke_token(const std::string& token) {
    return impl_->revoke_token(token);
}

std::optional<UserInfo> AuthManager::get_user_info(const std::string& user_id) {
    return impl_->get_user_info(user_id);
}

bool AuthManager::create_user(const UserInfo& user_info, const std::string& password) {
    return impl_->create_user(user_info, password);
}

bool AuthManager::update_user(const UserInfo& user_info) {
    return impl_->update_user(user_info);
}

bool AuthManager::delete_user(const std::string& user_id) {
    return impl_->delete_user(user_id);
}

bool AuthManager::lock_user(const std::string& user_id) {
    return impl_->lock_user(user_id);
}

bool AuthManager::unlock_user(const std::string& user_id) {
    return impl_->unlock_user(user_id);
}

bool AuthManager::change_password(
    const std::string& user_id,
    const std::string& old_password,
    const std::string& new_password) {
    return impl_->change_password(user_id, old_password, new_password);
}

bool AuthManager::reset_password(const std::string& user_id, const std::string& new_password) {
    return impl_->reset_password(user_id, new_password);
}

std::vector<std::string> AuthManager::get_active_sessions(const std::string& user_id) {
    return impl_->get_active_sessions(user_id);
}

bool AuthManager::terminate_session(const std::string& user_id, const std::string& session_token) {
    return impl_->terminate_session(user_id, session_token);
}

bool AuthManager::terminate_all_sessions(const std::string& user_id) {
    return impl_->terminate_all_sessions(user_id);
}

std::unordered_map<std::string, std::any> AuthManager::get_statistics() {
    return impl_->get_statistics();
}

AuthConfig AuthManager::get_config() const {
    return impl_->config_;
}

bool AuthManager::update_config(const AuthConfig& config) {
    return impl_->initialize(config);
}

} // namespace omop::security