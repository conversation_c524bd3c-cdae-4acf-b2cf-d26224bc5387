#include "authorization.h"
#include "common/logging.h"
#include <algorithm>
#include <regex>
#include <mutex>
#include <shared_mutex>
#include <sstream>
#include <random>

namespace omop::security {

namespace {
    // Helper to generate unique IDs
    std::string generate_unique_id(const std::string& prefix) {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::uniform_int_distribution<> dis(0, 999999);
        
        std::stringstream ss;
        ss << prefix << "_" << std::chrono::system_clock::now().time_since_epoch().count() 
           << "_" << dis(gen);
        return ss.str();
    }
    
    // Helper to evaluate simple policy conditions
    bool evaluate_condition(const std::string& condition,
                          const std::unordered_map<std::string, std::any>& context) {
        if (condition.empty() || condition == "true") {
            return true;
        }
        
        if (condition == "false") {
            return false;
        }
        
        // Simple condition parser for demonstration
        // Format: "key == value" or "key != value"
        std::regex condition_regex(R"((\w+)\s*(==|!=)\s*(.+))");
        std::smatch match;
        
        if (std::regex_match(condition, match, condition_regex)) {
            std::string key = match[1];
            std::string op = match[2];
            std::string expected_value = match[3];
            
            // Remove quotes if present
            if (expected_value.front() == '"' && expected_value.back() == '"') {
                expected_value = expected_value.substr(1, expected_value.length() - 2);
            }
            
            auto it = context.find(key);
            if (it != context.end()) {
                try {
                    std::string actual_value = std::any_cast<std::string>(it->second);
                    
                    if (op == "==") {
                        return actual_value == expected_value;
                    } else if (op == "!=") {
                        return actual_value != expected_value;
                    }
                } catch (const std::bad_any_cast&) {
                    // Type mismatch
                    return false;
                }
            }
        }
        
        // Default to false for unparseable conditions
        return false;
    }
    
    // Helper to check if resource matches pattern
    bool matches_pattern(const std::string& resource, const std::string& pattern) {
        if (pattern == "*" || pattern == resource) {
            return true;
        }
        
        // Convert pattern to regex (simple glob support)
        std::string regex_pattern = pattern;
        
        // Escape special regex characters except * and ?
        regex_pattern = std::regex_replace(regex_pattern, std::regex(R"([\.\+\^\$\(\)\[\]\{\}\|\\])"), R"(\$&)");
        
        // Convert glob patterns to regex
        regex_pattern = std::regex_replace(regex_pattern, std::regex(R"(\*)"), ".*");
        regex_pattern = std::regex_replace(regex_pattern, std::regex(R"(\?)"), ".");
        
        // Add anchors
        regex_pattern = "^" + regex_pattern + "$";
        
        try {
            std::regex re(regex_pattern);
            return std::regex_match(resource, re);
        } catch (const std::regex_error&) {
            // Invalid regex, fall back to exact match
            return resource == pattern;
        }
    }
    
    // Helper to check if action matches pattern
    bool action_matches(const std::string& action, const std::string& pattern) {
        if (pattern == "*" || pattern == action) {
            return true;
        }
        
        // Support comma-separated actions
        std::stringstream ss(pattern);
        std::string allowed_action;
        while (std::getline(ss, allowed_action, ',')) {
            // Trim whitespace
            allowed_action.erase(0, allowed_action.find_first_not_of(" \t"));
            allowed_action.erase(allowed_action.find_last_not_of(" \t") + 1);
            
            if (allowed_action == action) {
                return true;
            }
        }
        
        return false;
    }
    
    // Helper to check if user has role
    bool user_has_role(const std::vector<std::string>& user_roles, const std::string& required_role) {
        return std::find(user_roles.begin(), user_roles.end(), required_role) != user_roles.end();
    }
    
    // Helper to check if user has any of the required roles
    bool user_has_any_role(const std::vector<std::string>& user_roles, const std::vector<std::string>& required_roles) {
        for (const auto& required_role : required_roles) {
            if (user_has_role(user_roles, required_role)) {
                return true;
            }
        }
        return false;
    }
    
    // Helper to check if user has all required roles
    bool user_has_all_roles(const std::vector<std::string>& user_roles, const std::vector<std::string>& required_roles) {
        for (const auto& required_role : required_roles) {
            if (!user_has_role(user_roles, required_role)) {
                return false;
            }
        }
        return true;
    }
}

// PIMPL implementation for AuthorizationManager
class AuthorizationManager::Impl {
public:
    Impl() = default;
    ~Impl() = default;
    
    AuthzConfig config_;
    bool initialized_ = false;
    std::shared_ptr<spdlog::logger> logger_;
    mutable std::shared_mutex policies_mutex_;
    
    // In-memory storage (in production, would use database)
    std::vector<Policy> policies_;
    std::unordered_map<std::string, Role> roles_;
    std::unordered_map<std::string, std::vector<std::string>> user_roles_;
    
    bool initialize(const AuthzConfig& config) {
        std::unique_lock<std::shared_mutex> lock(policies_mutex_);
        
        config_ = config;
        
        if (!config_.enabled) {
            initialized_ = true;
            return true;
        }
        
        try {
            logger_ = common::Logger::get("authorization_manager");
            logger_->info("AuthorizationManager initialized");
            
            // Load default policies if configured
            if (config_.load_default_policies) {
                load_default_policies();
            }
            
            initialized_ = true;
            return true;
        } catch (const std::exception& e) {
            spdlog::error("Failed to initialize AuthorizationManager: {}", e.what());
            return false;
        }
    }
    
    void load_default_policies() {
        // Create default admin role
        Role admin_role;
        admin_role.id = "admin";
        admin_role.name = "Administrator";
        admin_role.description = "Full system administrator";
        admin_role.permission_ids = {"admin_permission"}; // All resources, all actions, all conditions
        roles_["admin"] = admin_role;
        
        // Create default user role
        Role user_role;
        user_role.id = "user";
        user_role.name = "Standard User";
        user_role.description = "Standard user with limited permissions";
        user_role.permission_ids = {"user_read_permission", "user_view_permission"};
        roles_["user"] = user_role;
        
        // Create default policies
        Policy admin_policy;
        admin_policy.id = generate_unique_id("policy");
        admin_policy.name = "Admin Full Access";
        admin_policy.description = "Full access for administrators";
        admin_policy.effect = AuthzResult::Allow;
        admin_policy.priority = 100;
        admin_policy.subject_pattern = "admin";
        admin_policy.resource_pattern = "*";
        admin_policy.actions = {Action::Read, Action::Write, Action::Delete, Action::Create, Action::Update, Action::Execute, Action::Admin};
        admin_policy.condition = "true";
        policies_.push_back(admin_policy);
        
        Policy user_policy;
        user_policy.id = generate_unique_id("policy");
        user_policy.name = "User Read Access";
        user_policy.description = "Read access for standard users";
        user_policy.effect = AuthzResult::Allow;
        user_policy.priority = 50;
        user_policy.subject_pattern = "user";
        user_policy.resource_pattern = "data:*|reports:*";
        user_policy.actions = {Action::Read};
        user_policy.condition = "true";
        policies_.push_back(user_policy);
        
        Policy deny_policy;
        deny_policy.id = generate_unique_id("policy");
        deny_policy.name = "Default Deny";
        deny_policy.description = "Deny all other access";
        deny_policy.effect = AuthzResult::Deny;
        deny_policy.priority = 0;
        deny_policy.subject_pattern = "*";
        deny_policy.resource_pattern = "*";
        deny_policy.actions = {Action::Read, Action::Write, Action::Delete, Action::Create, Action::Update, Action::Execute, Action::Admin};
        deny_policy.condition = "true";
        policies_.push_back(deny_policy);
        
        logger_->info("Loaded {} default policies", policies_.size());
    }
    
    bool check_permission(const std::string& user_id,
                         const std::string& resource,
                         const std::string& action,
                         const std::unordered_map<std::string, std::any>& context) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::shared_lock<std::shared_mutex> lock(policies_mutex_);
        
        try {
            // Get user roles
            auto user_roles_it = user_roles_.find(user_id);
            if (user_roles_it == user_roles_.end()) {
                logger_->warn("No roles found for user: {}", user_id);
                return false;
            }
            
            const auto& user_roles = user_roles_it->second;
            
            // Sort policies by priority (highest first)
            std::vector<Policy> sorted_policies = policies_;
            std::sort(sorted_policies.begin(), sorted_policies.end(),
                     [](const Policy& a, const Policy& b) {
                         return a.priority > b.priority;
                     });
            
            // Check each policy
            for (const auto& policy : sorted_policies) {
                if (policy_matches(policy, user_roles, resource, action, context)) {
                    logger_->debug("Policy {} matched for user {} on resource {} action {}", 
                                 policy.name, user_id, resource, action);
                    return policy.effect == AuthzResult::Allow;
                }
            }
            
            logger_->warn("No matching policy found for user {} on resource {} action {}", 
                         user_id, resource, action);
            return false;
            
        } catch (const std::exception& e) {
            logger_->error("Permission check error: {}", e.what());
            return false;
        }
    }
    
    bool policy_matches(const Policy& policy,
                       const std::vector<std::string>& user_roles,
                       const std::string& resource,
                       const std::string& action,
                       const std::unordered_map<std::string, std::any>& context) {
        // Check subject pattern
        if (!policy.subject_pattern.empty() && policy.subject_pattern != "*") {
            bool subject_matches = false;
            for (const auto& user_role : user_roles) {
                if (matches_pattern(user_role, policy.subject_pattern)) {
                    subject_matches = true;
                    break;
                }
            }
            if (!subject_matches) {
                return false;
            }
        }
        
        // Check resource pattern
        if (!matches_pattern(resource, policy.resource_pattern)) {
            return false;
        }
        
        // Check actions
        bool action_matches_found = false;
        Action requested_action = string_to_action(action);
        for (const auto& policy_action : policy.actions) {
            if (policy_action == requested_action) {
                action_matches_found = true;
                break;
            }
        }
        if (!action_matches_found) {
            return false;
        }

        // Check condition
        if (!policy.condition.empty() && policy.condition != "true") {
            if (!evaluate_condition(policy.condition, context)) {
                return false;
            }
        }
        
        return true;
    }
    
    bool add_policy(const Policy& policy) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::unique_lock<std::shared_mutex> lock(policies_mutex_);
        
        try {
            // Validate policy
            if (policy.name.empty() || policy.subject_pattern.empty() ||
                policy.resource_pattern.empty() || policy.actions.empty()) {
                logger_->warn("Invalid policy: missing required fields");
                return false;
            }
            
            // Check for duplicate policy name
            auto it = std::find_if(policies_.begin(), policies_.end(),
                                 [&](const Policy& p) { return p.name == policy.name; });
            if (it != policies_.end()) {
                logger_->warn("Policy with name {} already exists", policy.name);
                return false;
            }
            
            policies_.push_back(policy);
            logger_->info("Policy added: {}", policy.name);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Add policy error: {}", e.what());
            return false;
        }
    }
    
    bool update_policy(const std::string& policy_id, const Policy& updated_policy) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::unique_lock<std::shared_mutex> lock(policies_mutex_);
        
        try {
            auto it = std::find_if(policies_.begin(), policies_.end(),
                                 [&](const Policy& p) { return p.id == policy_id; });
            
            if (it == policies_.end()) {
                logger_->warn("Policy not found: {}", policy_id);
                return false;
            }
            
            *it = updated_policy;
            it->id = policy_id; // Preserve original ID
            
            logger_->info("Policy updated: {}", updated_policy.name);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Update policy error: {}", e.what());
            return false;
        }
    }
    
    bool delete_policy(const std::string& policy_id) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::unique_lock<std::shared_mutex> lock(policies_mutex_);
        
        try {
            auto it = std::find_if(policies_.begin(), policies_.end(),
                                 [&](const Policy& p) { return p.id == policy_id; });
            
            if (it == policies_.end()) {
                logger_->warn("Policy not found: {}", policy_id);
                return false;
            }
            
            std::string policy_name = it->name;
            policies_.erase(it);
            
            logger_->info("Policy deleted: {}", policy_name);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Delete policy error: {}", e.what());
            return false;
        }
    }
    
    std::vector<Policy> get_policies() const {
        if (!initialized_ || !config_.enabled) {
            return {};
        }
        
        std::shared_lock<std::shared_mutex> lock(policies_mutex_);
        return policies_;
    }
    
    std::optional<Policy> get_policy(const std::string& policy_id) const {
        if (!initialized_ || !config_.enabled) {
            return std::nullopt;
        }
        
        std::shared_lock<std::shared_mutex> lock(policies_mutex_);
        
        auto it = std::find_if(policies_.begin(), policies_.end(),
                             [&](const Policy& p) { return p.id == policy_id; });
        
        if (it != policies_.end()) {
            return *it;
        }
        
        return std::nullopt;
    }
    
    bool add_role(const Role& role) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::unique_lock<std::shared_mutex> lock(policies_mutex_);
        
        try {
            // Validate role
            if (role.id.empty() || role.name.empty()) {
                logger_->warn("Invalid role: missing required fields");
                return false;
            }
            
            // Check for duplicate role ID
            if (roles_.find(role.id) != roles_.end()) {
                logger_->warn("Role with ID {} already exists", role.id);
                return false;
            }
            
            roles_[role.id] = role;
            logger_->info("Role added: {}", role.name);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Add role error: {}", e.what());
            return false;
        }
    }
    
    bool update_role(const std::string& role_id, const Role& updated_role) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::unique_lock<std::shared_mutex> lock(policies_mutex_);
        
        try {
            auto it = roles_.find(role_id);
            if (it == roles_.end()) {
                logger_->warn("Role not found: {}", role_id);
                return false;
            }
            
            roles_[role_id] = updated_role;
            roles_[role_id].id = role_id; // Preserve original ID
            
            logger_->info("Role updated: {}", updated_role.name);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Update role error: {}", e.what());
            return false;
        }
    }
    
    bool delete_role(const std::string& role_id) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::unique_lock<std::shared_mutex> lock(policies_mutex_);
        
        try {
            auto it = roles_.find(role_id);
            if (it == roles_.end()) {
                logger_->warn("Role not found: {}", role_id);
                return false;
            }
            
            std::string role_name = it->second.name;
            roles_.erase(it);
            
            // Remove role from all users
            for (auto& [user_id, user_roles] : user_roles_) {
                user_roles.erase(std::remove(user_roles.begin(), user_roles.end(), role_id), 
                               user_roles.end());
            }
            
            logger_->info("Role deleted: {}", role_name);
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Delete role error: {}", e.what());
            return false;
        }
    }
    
    std::vector<Role> get_roles() const {
        if (!initialized_ || !config_.enabled) {
            return {};
        }
        
        std::shared_lock<std::shared_mutex> lock(policies_mutex_);
        
        std::vector<Role> result;
        result.reserve(roles_.size());
        for (const auto& [id, role] : roles_) {
            result.push_back(role);
        }
        
        return result;
    }
    
    std::optional<Role> get_role(const std::string& role_id) const {
        if (!initialized_ || !config_.enabled) {
            return std::nullopt;
        }
        
        std::shared_lock<std::shared_mutex> lock(policies_mutex_);
        
        auto it = roles_.find(role_id);
        if (it != roles_.end()) {
            return it->second;
        }
        
        return std::nullopt;
    }
    
    bool assign_role_to_user(const std::string& user_id, const std::string& role_id) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::unique_lock<std::shared_mutex> lock(policies_mutex_);
        
        try {
            // Check if role exists
            if (roles_.find(role_id) == roles_.end()) {
                logger_->warn("Role not found: {}", role_id);
                return false;
            }
            
            // Add role to user
            auto& user_roles = user_roles_[user_id];
            if (std::find(user_roles.begin(), user_roles.end(), role_id) == user_roles.end()) {
                user_roles.push_back(role_id);
                logger_->info("Role {} assigned to user {}", role_id, user_id);
            }
            
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Assign role error: {}", e.what());
            return false;
        }
    }
    
    bool remove_role_from_user(const std::string& user_id, const std::string& role_id) {
        if (!initialized_ || !config_.enabled) {
            return false;
        }
        
        std::unique_lock<std::shared_mutex> lock(policies_mutex_);
        
        try {
            auto it = user_roles_.find(user_id);
            if (it == user_roles_.end()) {
                return false;
            }
            
            auto& user_roles = it->second;
            auto role_it = std::find(user_roles.begin(), user_roles.end(), role_id);
            
            if (role_it != user_roles.end()) {
                user_roles.erase(role_it);
                logger_->info("Role {} removed from user {}", role_id, user_id);
                return true;
            }
            
            return false;
            
        } catch (const std::exception& e) {
            logger_->error("Remove role error: {}", e.what());
            return false;
        }
    }
    
    std::vector<std::string> get_user_roles(const std::string& user_id) const {
        if (!initialized_ || !config_.enabled) {
            return {};
        }
        
        std::shared_lock<std::shared_mutex> lock(policies_mutex_);
        
        auto it = user_roles_.find(user_id);
        if (it != user_roles_.end()) {
            return it->second;
        }
        
        return {};
    }
    
    std::unordered_map<std::string, std::any> get_statistics() const {
        if (!initialized_ || !config_.enabled) {
            return {};
        }
        
        std::shared_lock<std::shared_mutex> lock(policies_mutex_);
        
        std::unordered_map<std::string, std::any> stats;
        stats["total_policies"] = static_cast<int>(policies_.size());
        stats["total_roles"] = static_cast<int>(roles_.size());
        stats["total_users_with_roles"] = static_cast<int>(user_roles_.size());
        
        int total_role_assignments = 0;
        for (const auto& [user_id, user_roles] : user_roles_) {
            total_role_assignments += static_cast<int>(user_roles.size());
        }
        stats["total_role_assignments"] = total_role_assignments;
        
        return stats;
    }

    AuthzConfig get_config() const {
        return config_;
    }

    bool update_config(const AuthzConfig& config) {
        std::unique_lock<std::shared_mutex> lock(policies_mutex_);
        config_ = config;
        return true;
    }
};

AuthorizationManager::AuthorizationManager() : impl_(std::make_unique<Impl>()) {}

AuthorizationManager::~AuthorizationManager() = default;

bool AuthorizationManager::initialize(const AuthzConfig& config) {
    return impl_->initialize(config);
}

bool AuthorizationManager::check_permission(const std::string& user_id,
                                           const std::string& resource,
                                           const std::string& action,
                                           const std::unordered_map<std::string, std::any>& context) {
    return impl_->check_permission(user_id, resource, action, context);
}

bool AuthorizationManager::add_policy(const Policy& policy) {
    return impl_->add_policy(policy);
}

bool AuthorizationManager::update_policy(const std::string& policy_id, const Policy& updated_policy) {
    return impl_->update_policy(policy_id, updated_policy);
}

bool AuthorizationManager::delete_policy(const std::string& policy_id) {
    return impl_->delete_policy(policy_id);
}

std::vector<Policy> AuthorizationManager::get_policies() const {
    return impl_->get_policies();
}

std::optional<Policy> AuthorizationManager::get_policy(const std::string& policy_id) const {
    return impl_->get_policy(policy_id);
}

bool AuthorizationManager::add_role(const Role& role) {
    return impl_->add_role(role);
}

bool AuthorizationManager::update_role(const std::string& role_id, const Role& updated_role) {
    return impl_->update_role(role_id, updated_role);
}

bool AuthorizationManager::delete_role(const std::string& role_id) {
    return impl_->delete_role(role_id);
}

std::vector<Role> AuthorizationManager::get_roles() const {
    return impl_->get_roles();
}

std::optional<Role> AuthorizationManager::get_role(const std::string& role_id) const {
    return impl_->get_role(role_id);
}

bool AuthorizationManager::assign_role_to_user(const std::string& user_id, const std::string& role_id) {
    return impl_->assign_role_to_user(user_id, role_id);
}

bool AuthorizationManager::remove_role_from_user(const std::string& user_id, const std::string& role_id) {
    return impl_->remove_role_from_user(user_id, role_id);
}

std::vector<std::string> AuthorizationManager::get_user_roles(const std::string& user_id) const {
    return impl_->get_user_roles(user_id);
}

std::unordered_map<std::string, std::any> AuthorizationManager::get_statistics() const {
    return impl_->get_statistics();
}

AuthzConfig AuthorizationManager::get_config() const {
    return impl_->get_config();
}

bool AuthorizationManager::update_config(const AuthzConfig& config) {
    return impl_->update_config(config);
}

// Utility functions
std::string action_to_string(Action action) {
    switch (action) {
        case Action::Read: return "read";
        case Action::Write: return "write";
        case Action::Delete: return "delete";
        case Action::Create: return "create";
        case Action::Update: return "update";
        case Action::Execute: return "execute";
        case Action::Admin: return "admin";
        default: return "unknown";
    }
}

Action string_to_action(const std::string& action_str) {
    if (action_str == "read") return Action::Read;
    if (action_str == "write") return Action::Write;
    if (action_str == "delete") return Action::Delete;
    if (action_str == "create") return Action::Create;
    if (action_str == "update") return Action::Update;
    if (action_str == "execute") return Action::Execute;
    if (action_str == "admin") return Action::Admin;
    return Action::Read; // Default fallback
}

std::string resource_type_to_string(ResourceType resource_type) {
    switch (resource_type) {
        case ResourceType::ETLJob: return "etl_job";
        case ResourceType::Pipeline: return "pipeline";
        case ResourceType::Dataset: return "dataset";
        case ResourceType::Configuration: return "configuration";
        case ResourceType::User: return "user";
        case ResourceType::Role: return "role";
        case ResourceType::System: return "system";
        case ResourceType::API: return "api";
        case ResourceType::Database: return "database";
        case ResourceType::File: return "file";
        default: return "unknown";
    }
}

ResourceType string_to_resource_type(const std::string& resource_type_str) {
    if (resource_type_str == "etl_job") return ResourceType::ETLJob;
    if (resource_type_str == "pipeline") return ResourceType::Pipeline;
    if (resource_type_str == "dataset") return ResourceType::Dataset;
    if (resource_type_str == "configuration") return ResourceType::Configuration;
    if (resource_type_str == "user") return ResourceType::User;
    if (resource_type_str == "role") return ResourceType::Role;
    if (resource_type_str == "system") return ResourceType::System;
    if (resource_type_str == "api") return ResourceType::API;
    if (resource_type_str == "database") return ResourceType::Database;
    if (resource_type_str == "file") return ResourceType::File;
    return ResourceType::File; // Default fallback
}

std::unique_ptr<IAuthorizationManager> create_authorization_manager() {
    return std::make_unique<AuthorizationManager>();
}

AuthzConfig get_default_authz_config() {
    AuthzConfig config;
    config.enabled = true;
    config.enable_rbac = true;
    config.enable_abac = false;
    config.enable_policy_engine = true;
    config.load_default_policies = true;
    config.default_policy = "deny";
    config.max_cache_size = 10000;
    config.policy_language = "simple";
    return config;
}

} // namespace omop::security