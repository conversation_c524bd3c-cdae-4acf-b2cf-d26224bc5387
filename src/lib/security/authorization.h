#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <any>
#include <functional>
#include <optional>

namespace omop::security {

/**
 * @brief Authorization result enumeration
 */
enum class AuthzResult {
    Allow,
    Deny,
    NotFound,
    Error
};

/**
 * @brief Permission action enumeration
 */
enum class Action {
    Read,
    Write,
    Delete,
    Create,
    Update,
    Execute,
    Admin
};

/**
 * @brief Resource type enumeration
 */
enum class ResourceType {
    ETLJob,
    Pipeline,
    Dataset,
    Configuration,
    User,
    Role,
    System,
    API,
    Database,
    File
};

/**
 * @brief Permission structure
 */
struct Permission {
    std::string id;
    std::string name;
    std::string description;
    ResourceType resource_type;
    std::string resource_pattern;
    std::vector<Action> actions;
    std::unordered_map<std::string, std::any> conditions;
    bool is_system_permission{false};
};

/**
 * @brief Role structure
 */
struct Role {
    std::string id;
    std::string name;
    std::string description;
    std::vector<std::string> permission_ids;
    std::vector<std::string> parent_role_ids;
    std::unordered_map<std::string, std::any> metadata;
    bool is_system_role{false};
};

/**
 * @brief Policy structure
 */
struct Policy {
    std::string id;
    std::string name;
    std::string description;
    std::string subject_pattern;
    std::string resource_pattern;
    std::vector<Action> actions;
    std::string condition;
    AuthzResult effect{AuthzResult::Allow};
    int priority{100};
    bool is_active{true};
};

/**
 * @brief Authorization request structure
 */
struct AuthzRequest {
    std::string subject;
    std::string resource;
    ResourceType resource_type;
    Action action;
    std::unordered_map<std::string, std::any> context;
};

/**
 * @brief Authorization response structure
 */
struct AuthzResponse {
    AuthzResult result;
    std::string reason;
    std::vector<std::string> applied_policies;
    std::unordered_map<std::string, std::any> metadata;
};

/**
 * @brief Authorization configuration
 */
struct AuthzConfig {
    bool enabled{true};
    bool enable_rbac{true};
    bool enable_abac{false};
    bool enable_policy_engine{true};
    bool load_default_policies{true};
    std::string default_policy{"deny"};
    std::chrono::seconds cache_ttl{300};
    size_t max_cache_size{10000};
    std::string policy_language{"rego"};
    std::vector<std::string> policy_files;
    std::unordered_map<std::string, std::any> additional_config;
};

/**
 * @brief Authorization manager interface
 * 
 * This interface defines the contract for authorization managers that handle
 * access control, permissions, roles, and policies.
 */
class IAuthorizationManager {
public:
    virtual ~IAuthorizationManager() = default;

    /**
     * @brief Initialize authorization manager
     * @param config Authorization configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const AuthzConfig& config) = 0;

    /**
     * @brief Check authorization for request
     * @param request Authorization request
     * @return AuthzResponse Authorization response
     */
    virtual AuthzResponse authorize(const AuthzRequest& request) = 0;

    /**
     * @brief Check if subject has permission
     * @param subject Subject identifier
     * @param resource Resource identifier
     * @param action Action to perform
     * @return bool True if authorized
     */
    virtual bool is_authorized(
        const std::string& subject,
        const std::string& resource,
        Action action) = 0;

    /**
     * @brief Get subject permissions
     * @param subject Subject identifier
     * @return std::vector<Permission> List of permissions
     */
    virtual std::vector<Permission> get_permissions(const std::string& subject) = 0;

    /**
     * @brief Get subject roles
     * @param subject Subject identifier
     * @return std::vector<Role> List of roles
     */
    virtual std::vector<Role> get_roles(const std::string& subject) = 0;

    /**
     * @brief Create permission
     * @param permission Permission to create
     * @return bool True if permission created successfully
     */
    virtual bool create_permission(const Permission& permission) = 0;

    /**
     * @brief Update permission
     * @param permission Permission to update
     * @return bool True if permission updated successfully
     */
    virtual bool update_permission(const Permission& permission) = 0;

    /**
     * @brief Delete permission
     * @param permission_id Permission ID to delete
     * @return bool True if permission deleted successfully
     */
    virtual bool delete_permission(const std::string& permission_id) = 0;

    /**
     * @brief Get permission by ID
     * @param permission_id Permission ID
     * @return std::optional<Permission> Permission if exists
     */
    virtual std::optional<Permission> get_permission(const std::string& permission_id) = 0;

    /**
     * @brief Get all permissions
     * @return std::vector<Permission> List of all permissions
     */
    virtual std::vector<Permission> get_all_permissions() = 0;

    /**
     * @brief Create role
     * @param role Role to create
     * @return bool True if role created successfully
     */
    virtual bool create_role(const Role& role) = 0;

    /**
     * @brief Update role
     * @param role Role to update
     * @return bool True if role updated successfully
     */
    virtual bool update_role(const Role& role) = 0;

    /**
     * @brief Delete role
     * @param role_id Role ID to delete
     * @return bool True if role deleted successfully
     */
    virtual bool delete_role(const std::string& role_id) = 0;

    /**
     * @brief Get role by ID
     * @param role_id Role ID
     * @return std::optional<Role> Role if exists
     */
    virtual std::optional<Role> get_role(const std::string& role_id) = 0;

    /**
     * @brief Get all roles
     * @return std::vector<Role> List of all roles
     */
    virtual std::vector<Role> get_all_roles() = 0;

    /**
     * @brief Assign role to subject
     * @param subject Subject identifier
     * @param role_id Role ID
     * @return bool True if role assigned successfully
     */
    virtual bool assign_role(const std::string& subject, const std::string& role_id) = 0;

    /**
     * @brief Remove role from subject
     * @param subject Subject identifier
     * @param role_id Role ID
     * @return bool True if role removed successfully
     */
    virtual bool remove_role(const std::string& subject, const std::string& role_id) = 0;

    /**
     * @brief Grant permission to subject
     * @param subject Subject identifier
     * @param permission_id Permission ID
     * @return bool True if permission granted successfully
     */
    virtual bool grant_permission(const std::string& subject, const std::string& permission_id) = 0;

    /**
     * @brief Revoke permission from subject
     * @param subject Subject identifier
     * @param permission_id Permission ID
     * @return bool True if permission revoked successfully
     */
    virtual bool revoke_permission(const std::string& subject, const std::string& permission_id) = 0;

    /**
     * @brief Create policy
     * @param policy Policy to create
     * @return bool True if policy created successfully
     */
    virtual bool create_policy(const Policy& policy) = 0;

    /**
     * @brief Update policy
     * @param policy Policy to update
     * @return bool True if policy updated successfully
     */
    virtual bool update_policy(const Policy& policy) = 0;

    /**
     * @brief Delete policy
     * @param policy_id Policy ID to delete
     * @return bool True if policy deleted successfully
     */
    virtual bool delete_policy(const std::string& policy_id) = 0;

    /**
     * @brief Get policy by ID
     * @param policy_id Policy ID
     * @return std::optional<Policy> Policy if exists
     */
    virtual std::optional<Policy> get_policy(const std::string& policy_id) = 0;

    /**
     * @brief Get all policies
     * @return std::vector<Policy> List of all policies
     */
    virtual std::vector<Policy> get_all_policies() = 0;

    /**
     * @brief Evaluate policy expression
     * @param expression Policy expression
     * @param context Evaluation context
     * @return bool True if expression evaluates to true
     */
    virtual bool evaluate_policy(
        const std::string& expression,
        const std::unordered_map<std::string, std::any>& context) = 0;

    /**
     * @brief Get authorization statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() = 0;

    /**
     * @brief Get authorization configuration
     * @return AuthzConfig Current configuration
     */
    virtual AuthzConfig get_config() const = 0;

    /**
     * @brief Update authorization configuration
     * @param config New configuration
     * @return bool True if configuration updated successfully
     */
    virtual bool update_config(const AuthzConfig& config) = 0;
};

/**
 * @brief Default authorization manager implementation
 */
class AuthorizationManager : public IAuthorizationManager {
public:
    AuthorizationManager() = default;
    ~AuthorizationManager() override = default;

    bool initialize(const AuthzConfig& config) override;
    AuthzResponse authorize(const AuthzRequest& request) override;
    bool is_authorized(const std::string& subject, const std::string& resource, Action action) override;

    std::vector<Permission> get_permissions(const std::string& subject) override;
    std::vector<Role> get_roles(const std::string& subject) override;

    bool create_permission(const Permission& permission) override;
    bool update_permission(const Permission& permission) override;
    bool delete_permission(const std::string& permission_id) override;
    std::optional<Permission> get_permission(const std::string& permission_id) override;
    std::vector<Permission> get_all_permissions() override;

    bool create_role(const Role& role) override;
    bool update_role(const Role& role) override;
    bool delete_role(const std::string& role_id) override;
    std::optional<Role> get_role(const std::string& role_id) override;
    std::vector<Role> get_all_roles() override;

    bool assign_role(const std::string& subject, const std::string& role_id) override;
    bool remove_role(const std::string& subject, const std::string& role_id) override;
    bool grant_permission(const std::string& subject, const std::string& permission_id) override;
    bool revoke_permission(const std::string& subject, const std::string& permission_id) override;

    bool create_policy(const Policy& policy) override;
    bool update_policy(const Policy& policy) override;
    bool delete_policy(const std::string& policy_id) override;
    std::optional<Policy> get_policy(const std::string& policy_id) override;
    std::vector<Policy> get_all_policies() override;

    bool evaluate_policy(
        const std::string& expression,
        const std::unordered_map<std::string, std::any>& context) override;

    std::unordered_map<std::string, std::any> get_statistics() override;
    AuthzConfig get_config() const override;
    bool update_config(const AuthzConfig& config) override;

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief Create authorization manager instance
 * @return std::unique_ptr<IAuthorizationManager> Authorization manager instance
 */
std::unique_ptr<IAuthorizationManager> create_authorization_manager();

/**
 * @brief Get default authorization configuration
 * @return AuthzConfig Default configuration
 */
AuthzConfig get_default_authz_config();

/**
 * @brief Convert action to string
 * @param action Action enum
 * @return std::string Action string
 */
std::string action_to_string(Action action);

/**
 * @brief Convert string to action
 * @param action_str Action string
 * @return Action Action enum
 */
Action string_to_action(const std::string& action_str);

/**
 * @brief Convert resource type to string
 * @param resource_type Resource type enum
 * @return std::string Resource type string
 */
std::string resource_type_to_string(ResourceType resource_type);

/**
 * @brief Convert string to resource type
 * @param resource_type_str Resource type string
 * @return ResourceType Resource type enum
 */
ResourceType string_to_resource_type(const std::string& resource_type_str);

} // namespace omop::security