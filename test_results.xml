<?xml version="1.0" encoding="UTF-8"?>
<testsuites tests="1" failures="1" disabled="0" errors="0" time="0.008" timestamp="2025-07-24T16:48:20.346" name="AllTests">
  <testsuite name="JsonExtractorTest" tests="1" failures="1" disabled="0" skipped="0" errors="0" time="0.008" timestamp="2025-07-24T16:48:20.346">
    <testcase name="MaxDepthLimit" file="/workspace/tests/unit/extract/json_extractor_test.cpp" line="415" status="run" result="completed" time="0.008" timestamp="2025-07-24T16:48:20.346" classname="JsonExtractorTest">
      <failure message="unknown file&#x0A;C++ exception with description &quot;bad any_cast&quot; thrown in the test body.&#x0A;" type=""><![CDATA[unknown file
C++ exception with description "bad any_cast" thrown in the test body.
]]></failure>
    </testcase>
  </testsuite>
</testsuites>
